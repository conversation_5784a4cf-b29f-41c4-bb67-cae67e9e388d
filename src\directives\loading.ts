import type { Directive, DirectiveBinding } from 'vue'

/**
 * 加载状态指令
 * 用法：
 * v-loading="isLoading" - 显示/隐藏加载状态
 * v-loading.text="'加载中...'" - 自定义加载文本
 * v-loading.spinner - 使用旋转器样式
 * v-loading.overlay - 使用遮罩层
 */
const loadingDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    createLoadingElement(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    updateLoadingState(el, binding)
  },
  beforeUnmount(el: HTMLElement) {
    removeLoadingElement(el)
  }
}

interface LoadingElement extends HTMLElement {
  __v_loading?: HTMLElement
}

function createLoadingElement(el: LoadingElement, binding: DirectiveBinding) {
  if (el.__v_loading) {
    removeLoadingElement(el)
  }

  const loadingEl = document.createElement('div')
  loadingEl.className = 'v-loading'
  
  // 设置样式
  Object.assign(loadingEl.style, {
    position: 'absolute',
    top: '0',
    left: '0',
    right: '0',
    bottom: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: binding.modifiers.overlay ? 'rgba(255, 255, 255, 0.8)' : 'transparent',
    zIndex: '9999',
    pointerEvents: 'none'
  })

  // 创建加载内容
  const content = document.createElement('div')
  content.className = 'v-loading-content'
  
  if (binding.modifiers.spinner) {
    // 旋转器样式
    content.innerHTML = `
      <div style="
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #409eff;
        border-radius: 50%;
        animation: v-loading-spin 1s linear infinite;
      "></div>
    `
  } else {
    // 默认文本样式
    const text = typeof binding.value === 'string' ? binding.value : '加载中...'
    content.textContent = text
    content.style.color = '#409eff'
    content.style.fontSize = '14px'
  }

  loadingEl.appendChild(content)
  
  // 确保父元素有相对定位
  if (getComputedStyle(el).position === 'static') {
    el.style.position = 'relative'
  }

  el.__v_loading = loadingEl
  updateLoadingState(el, binding)
}

function updateLoadingState(el: LoadingElement, binding: DirectiveBinding) {
  if (!el.__v_loading) return

  const isLoading = Boolean(binding.value)
  
  if (isLoading) {
    if (!el.contains(el.__v_loading)) {
      el.appendChild(el.__v_loading)
    }
    el.__v_loading.style.display = 'flex'
  } else {
    el.__v_loading.style.display = 'none'
  }
}

function removeLoadingElement(el: LoadingElement) {
  if (el.__v_loading) {
    if (el.contains(el.__v_loading)) {
      el.removeChild(el.__v_loading)
    }
    delete el.__v_loading
  }
}

// 添加CSS动画
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = `
    @keyframes v-loading-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `
  document.head.appendChild(style)
}

export default loadingDirective
