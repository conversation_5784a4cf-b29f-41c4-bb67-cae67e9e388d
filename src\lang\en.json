{"登出": "logout", "登录": "login", "请输入账号": "Please enter your account number", "请输入密码": "please enter the password", "请输入用户名称!": "Please enter username!", "返回首页": "home", "返回": "return", "测试": "test", "开始起草": "begin drafting", "合同审查": "contract review", "合同起草": "contract drafting", "上传合同": "Upload contract", "输入合同信息，AI辅助生成，高效又省心": "Enter contract information and AI-assisted generation, efficient and worry-free", "上传合同文件，AI审查，高效识别潜在风险": "Upload contract documents, AI review, and efficiently identify potential risks", "合同名称": "contract name", "请输入合同名称，例:XX保密协议": "Please enter the contract name, for example:XX Confidentiality Agreement", "合同背景与目的": "Contract background and purpose", "请输入起草合同的背景与目的": "Please enter the background and purpose of drafting the contract", "合同立场": "Contract position", "请输入合同立场": "Please enter contract position", "参考合同": "reference contract", "取消": "cancel", "下一步": "next step", "请输入手机号": "Please enter your mobile number", "请输入验证码": "Please enter Captcha", "登 录 / 注 册": "Registration/Registration", "已阅读并同意": "have read and agree to", "服务协议": "service agreement", "和": "and", "隐私政策": "Privacy Policy", "微信扫码登录": "WeChat scan code login", "扫码默认阅读并同意": "Scan the code by default, read and agree", "个人中心": "personal Center", "成员管理": "member management", "个人/企业切换": "Personal/Business Switch", "退出登录": "log out", "查看更多": "view more", "查看": "Views", "删除": "delete", "操作": "operation", "合同文档": "contract document", "状态": "state", "审查状态": "review status", "创建时间": "creation time", "无需等待文件完全处理，可点击查看详情": "No need to wait for the file to be completely processed, you can click to view details", "我知道了": "I see", "确认删除审查记录吗?": "Are you sure to delete the review record?", "提示": "prompt", "删除成功": "deleted successfully", "合同列表": "contract list", "查询": "query", "重置": "reset", "请选择": "please select", "请输入": "please enter", "确认": "confirmed", "发送验证码": "Send Captcha", "合同摘要": "contract summary", "风险情况": "risk situation", "智能问答": "intelligent question and answer", "正在提取摘要中": "Extracting summary", "以上内容由AI生成": "The above content is generated by AI", "更多": "more", "展开全部": "expand all", "选定文中位置添加": "Select a location in the text to add", "复制": "replication", "原文": "original text", "条款缺失": "Missing clause", "审查清单": "review list", "合同解析中": "Contract analysis", "审查立场": "Review position", "自动生成审查清单": "Automatically generate review lists", "发起审查": "Initiate review", "审查规则": "review rules", "添加审查规则": "Add review rules", "全选": "select all", "已选择": "has elected", "项": "one", "请输入审查项名称": "Please enter the name of the review item", "请输入审查规则": "Please enter review rules", "新增审查项": "New review items", "是否人工确认AI生成的审查清单": "Whether the review list generated by AI is manually confirmed", "设置": "set", "审查目的": "review purposes", "请明确您的审查目的，这将帮助我们为您提供更精准和有效的审查服务": "Please clarify your review purpose, which will help us provide you with more accurate and effective review services", "生成中……": "Generating…", "添加审查目的": "Add review purpose", "未匹配到审查清单，自动勾选“自动生成审查清单": "If the review list is not matched,\"Automatically generate review list\" is automatically checked", "由AI生成": "Generated by AI", "审查立场决定审查的利益导向和侧重点": "Review position determines the interest orientation and focus of review", "未匹配到审查清单，自动勾选“自动生成审查清单”": "If the review list is not matched,\"Automatically generate review list\" is automatically checked", "删除后无法恢复，是否继续删除？": "It cannot be restored after deletion. Do you want to continue deleting?", "企业管理": "enterprise management", "个人版本": "personal version", "企业版本": "enterprise version", "确定": "determine", "确认删除吗？": "Confirm deletion?", "点赞": "praise", "点踩": "Click on", "你觉得哪些方面令你不满意？": "What aspects do you find unsatisfactory?", "请输入反馈内容": "Please enter your feedback content", "提交": "submitted", "发送中...": "Sending...", "chat.btns.cancel": "chat.btns.cancel", "收起": "put away", "请输入审查目的": "Please enter review purpose", "二次确认审查清单适用于专业使用者，": "The secondary confirmation review list is applicable to professional users,", "开启后可在审查前先对审查清单进行校验确认后，": "After opening, you can check and confirm the review list before review.", "再进行审查": "and then review it.", "撤销修改": "Undo modification", "接受修订": "under revision", "原文定位": "Original text positioning", "定位": "positioning", "风险": "risk", "收起展开": "retract and expand", "编辑审查项": "Editorial review items", "审查项名称": "Name of review item", "审查合同是否约定了卖方的收款账户信息，包括银行账户名、开户行名称、银行账号三项信息。": "Review whether the contract stipulates the seller's collection account information, including bank account name, opening bank name, and bank account number.", "若前述三项信息均未约定或未全部约定，请提示风险；若未约定，则无风险。": "If none or all of the above three items of information are agreed upon, please remind us of the risks; if not agreed upon, there is no risk.", "查看 “收款账户信息缺失” 示例": "View the example of \"Missing Collection Account Information\"", "复制建议": "Copy advice", "测试页面": "test page", "查看全部": "see all", "这是一段中文内容": "This is a Chinese paragraph", "动态类名": "dynamic class name", "对话框内容": "dialog content", "请输入用户名": "please enter your username", "对话框标题": "dialog box title", "自定义标题栏": "custom title blocks", "扫码登录": "the code scanning login", "验证码登录": "<PERSON><PERSON> login", "后台管理": "background management", "账号设置": "account settings", "手机号": "mobile phone number", "关于产品": "on product", "第三方信息共享清单": "Third Party Information Sharing List", "联系我们": "Contact us", "注销账号": "cancel the account", "退出登录不会丢失任何数据，你仍可以登录此账号": "You won't lose any data if you sign out, you can still sign in to this account", "确认退出登录": "Confirm login", "至": "to", "开始日期": "start date", "结束日期": "end date", "合同": "Contract", "下载": "download", "合同修改后才可进行复审": "The contract can be reviewed only after it is revised", "欢迎使用 iTerms": "Welcome to iTerms", "我可以帮您审查合同，或者直接问我法律问题~~~": "I can help you review the contract, or ask me legal questions directly ~~", "你可以这样问": "you can ask", "以上内容由人工智能模型生成, 仅作参考": "The above content is generated by an artificial intelligence model and is for reference only", "切换版本": "switch versions", "深度思考(R1)": "Deep Thinking (R1)", "联网搜索": "Online search", "请输入法律相关问题进行问答": "Please enter legal questions for Q & A", "支持png、jpeg、jpg、pdf、docx等": "Support png, jpeg, jpg, pdf, docx, etc.", "点击终止生成": "Click to terminate generation", "上传中...": "Uploading...", "上传失败...": "Upload failed...", "角色管理": "role management", "组织管理": "organization management", "管理后台": "management background", "文档": "document", "原稿文档": "original document", "改动文档": "Change documents", "文本比对": "text comparison", "语义比对": "Semantic comparison", "审查清单生成中": "Review list being generated", "审查进度": "review progress", "修改": "modify", "问答设置": "Q & A settings", "问答浮窗": "Question and answer floating window", "引用内容": "referenced content", "施行日期": "date of implementation", "本院认为": "the court believes", "本院查明": "The court found out", "裁判结果": "referee results", "暂无详情": "No details", "(已终止生成)": "(Generation has been terminated)", "系统繁忙，暂时无法为您解答": "The system is busy and cannot answer you for the time being", "历史问答": "History Q & A", "名称": "name", "备注": "remarks", "更新时间": "update time", "角色名称": "role name", "请输入角色名称": "Please enter role name", "开始时间": "start time", "结束时间": "end time", "权限设置": "permission settings", "角色信息": "role information", "数据权限": "data permission", "设置角色名称": "Set role name", "角色备注": "Role Notes", "请输入角色备注": "Please enter role comments", "重新上传": "re-upload", "近期比对": "Recent comparison", "文件名称": "file name", "完成时间": "completion time", "比对状态": "comparison status", "导出差异结果": "Export difference results", "只导出右侧比对差异结果，格式为excel": "Only the right comparison difference results are exported in excel format", "导出批注版本": "Export annotation version", "把差异结果以批注插入到左侧合同中": "Insert the difference result as a comment into the contract on the left", "比对差异：": "Comparison differences:", "新增": "new", "相似度：": "Similarity:", "查找": "find", "替换": "replaced", "搜索": "search", "获取文件地址": "Get file address", "修订": "revised", "重审合同": "Review of contract", "下载合同": "Download contract", "下载审查意见书": "Download review opinion", "忽略标点符号": "Ignore punctuation marks", "忽略页眉、页脚、页码": "Ignore headers, footers, page numbers", "忽略水印": "Ignore watermarks", "查 询": "enquiries", "重 置": "reset", "同步滚动": "scrolls in synchronization", "保存": "save", "测试不备份": "Test without backup", "这是测试文本": "This is the test text", "测试备份功能": "Test backup capabilities", "需要备份的文本": "Text to be backed up", "退出登录成功": "Log out successfully", "登出失败:": "Checkout failed:", "退出登录失败，请稍后再试": "<PERSON><PERSON> failed. Please try again later", "密码不能为空": "password cannot be empty", "操作成功": "operation is successful", "操作失败": "operation failed", "呢可能扣分砍脑壳看能否你可快你噶嫩凯哥哥跟可归纳可": "Well, maybe you can deduct points and chop off your skull to see if you can do it quickly, your brother <PERSON><PERSON><PERSON><PERSON> can sum it up", "请输入用户名称！": "Please enter your user name!", "修改成功": "modification is successful", "修改失败，请稍后再试": "Modification failed. Please try again later", "阿策": "Atse", "数据": "data", "问题": "problem", "未找到文本": "No text found", "请先登录": "if you signed in", "(审查意见书)": "(Review Opinion)", "WPS内容变化": "WPS content changes", "被打成轻伤，如何向对方索赔？": "If you are slightly injured, how can you claim compensation from the other party?", "医疗纠纷中，举证责任是由谁来承担？": "In medical disputes, who bears the burden of proof?", "加载对话失败": "Failed to load session", "获取聊天列表失败:": "Failed to obtain chat list:", "获取用户信息失败，但切换成功:": "Failed to obtain user information, but the switch was successful:", "切换失败": "handover failure", "切换请求被取消:": "Switch request was cancelled:", "操作被取消，请重试": "The operation was cancelled, please try again", "切换失败，请稍后重试": "Switching failed. Please try again later", "切换成功": "handover success", "退出后，需要重新登录才能使用。是否登出？": "After logging out, you need to log in again to use it. Do you want to log out?", "确认登出": "Confirm check-out", "问答": "Q & A", "创建比对": "Create a comparison", "比对列表": "comparison list", "路由变化:": "Routing changes:", "菜单高亮路径:": "Menu highlight path:", "当前菜单:": "Current menu:", "出现异常，请稍后再试": "An exception occurred. Please try again later", "请输入正确的名称": "Please enter the correct name", "组织权限管理": "Organizational authority management", "返回按钮被点击": "The back button was clicked", "语义对比失败": "Semantic comparison failed", "请先上传原稿文档": "Please upload the original document first", "请先上传比对文档": "Please upload the comparison document first", "比对失败，请稍后重试": "Comparison failed. Please try again later", "格式错误": "malformed", "第一阶段：深度理解审查目标": "Stage 1: Deep understanding of review goals", "正在接收审查任务指令": "Receiving review mission instructions", "正在识别核心审查对象与范围": "Core review objects and scope are being identified", "正在解析任务背后的关键意图": "Analyzing the key intentions behind the mission", "正在设定审查清单的成功标准": "Success criteria for review checklist are being set", "第二阶段：构建审查框架": "Phase 2: Build a review framework", "正在调用相关领域的知识库": "Calling knowledge bases in related fields", "正在提取关键节点与逻辑关系": "Extracting key nodes and logical relationships", "正在构建多层级的审查结构": "A multi-level review structure is being built", "正在规划审查清单的核心模块": "The core modules of the review list are being planned", "第三阶段：生成并优化审查项": "Phase 3: Generating and optimizing review items", "正在生成初步的审查项草案": "Preliminary draft review items are being generated", "正在评估并优化每个步骤的清晰度": "The clarity of each step is being evaluated and optimized", "正在确保每个审查项的可执行性": "The enforceability of each review item is being ensured", "正在补充可能存在的逻辑漏洞": "Possible logic loopholes are being filled", "正在进行最终的格式化与润色": "Final formatting and polishing are underway", "即将完成，审查清单即将呈现！": "Almost completed, the review list is about to be presented!", "复制成功": "replicate successful", "未能定位到原文位置": "Failed to locate the original text", "请先划选原文位置": "Please select the original position first", "删除后，则不会审查该项，是否要删除？": "After deleting, the item will not be reviewed. Do you want to delete it?", "删除失败": "delete failed", "请填写审查目的": "Please fill in the review purpose", "审查目的不能重复": "Review purposes cannot be repeated", "审查清单生成失败，请稍后重试！": "The review list generation failed. Please try again later!", "AI审查出错:": "AI review error:", "请检查填写的审查目的是否正确": "Please check whether the review purpose filled in is correct", "请输入合同名称": "Please enter contract name", "长度不能小于2个字符": "The length cannot be less than 2 characters", "长度不能大于20个字符": "The length cannot be greater than 20 characters", "请输入合同背景与目的": "Please enter the contract background and purpose", "长度不能大于500个字符": "The length cannot be greater than 500 characters", "长度不能大于7个字符": "The length cannot be greater than 7 characters", "保存成功": "saved successfully", "请填写必填项": "Please fill in the required fields", "请选择文件": "please select a file", "上传失败": "upload failed", "没理解问题": "Didn't understand the problem", "编造信息": "fabricate information", "没有帮助": "not help", "其他": "other", "复制成功！": "Copy successfully!", "请选择反馈类型": "Please select the feedback type", "请输入反馈内容！": "Please enter the feedback content!", "网页": "web page", "案例": "case", "法规": "regulations", "实务文章": "Practical articles", "知识库": "knowledge base", "暂无内容": "no content", "获取模型列表失败": "Failed to get model list", "确定要删除该角色吗？": "Are you sure you want to delete this role?", "仅自己": "Only myself", "主部门": "main department", "仅含本级": "Including this level only", "含下属部门": "Including subordinate departments", "主部门及归属部门": "Main department and affiliated department", "及归属部门": "and affiliated department", "全部": "all", "您还没有保存修改的数据，确定要返回吗？": "You have not saved the modified data yet. Are you sure you want to return it?", "确认提示": "confirmation prompt", "查看比对记录": "View comparison records", "删除成功!": "Delete successfully!", "取消删除比对记录": "Cancel deleting comparison records", "排队中": "queue", "比对中": "in the alignment", "比对成功": "comparison is successful", "比对失败": "Comparison failed", "点击或拖拽文件到此处上传": "Click or drag the file here to upload", "只显示左侧文档的批注": "Display only annotations for the left document", "暂无数据": "no data", "网络错误": "network error", "gt字段不是字符串类型": "The gt field is not a string type", "获取文件stats失败": "Failed to get file stats", "彩云翻译配置不完整，请在 caiyunConfig.js 中设置token": "Caiyun Translation configuration is incomplete. Please set a token in caiyunConfig.js", "彩云翻译响应格式错误": "Caiyun translation response format is wrong", "彩云翻译请求失败:": "<PERSON><PERSON><PERSON>'s translation request failed:", "腾讯翻译配置不完整，请在 tencentConfig.js 中设置SecretId和SecretKey": "Tencent translation configuration is incomplete. Please set SecretId and SecretKey in tencentConfig.js", "腾讯翻译配置不完整，请在 tencentConfig.js 中设置region参数": "Tencent Translation configuration is incomplete. Please set the region parameter in tencentConfig.js", "腾讯翻译响应格式错误": "<PERSON><PERSON>'s translation response format is wrong", "腾讯翻译响应解析失败:": "Tencent translation response parsing failed:", "腾讯翻译请求失败:": "<PERSON><PERSON>'s translation request failed:", "腾讯翻译请求超时": "Tencent translation request timed out", "[WebOfficeSDK.init] mount挂载节点未找到": "[WebOfficeSDK.init] mount node not found", "[WebOfficeSDK.init] 不支持传递url，请使用appId、fileId、officeType、token等参数初始化！": "[WebOfficeSDK.init] does not support passing url. Please use appId, fileId, officeType, token and other parameters to initialize it!", "[WebOfficeSDK.init] appId、fileId、officeType为必选项！": "[WebOfficeSDK.init] appId, fileId, officeType are required options!", "[WebOfficeSDK.init] endpoint期望为字符串": "[WebOfficeSDK.init] Endpoint expectations are string", "[WebOfficeSDK.init] officeType属性值错误，可选值参考WebOfficeSDK.OfficeType:": "[WebOfficeSDK.init] The officeType property value is incorrect. For optional values, please refer to WebOfficeSDK.OfficeType:", "[WebOfficeSDK.init] token设置无效": "[WebOfficeSDK.init] Invalid token setting", "请输入正确的用户名信息": "Please enter the correct username information", "请输入6-50个字符": "Please enter 6-50 characters", "主动取消请求": "Proactive cancellation of request", "路由跳转取消请求": "Route jump cancellation request", "中断": "interrupt", "服务器内部错误": "Internal Server Error", "无权限访问": "Unauthorized access", "您的会话已过期，请重新登录": "Your session has expired. Please log in again.", "您的权限受到限制，请咨询管理员获取权限": "Your permissions are restricted. Please consult your administrator to obtain permissions.", "错误的参数或请求地址，请检查": "Wrong parameter or request address, please check", "网络异常，请稍后再试": "Network exception. Please try again later", "极验验证关闭": "Extreme verification is off", "极验验证码初始化失败": "Verification Captcha initialization failed", "手机号格式不正确": "The mobile phone number format is incorrect", "验证码长度不正确": "Captcha length is incorrect", "验证码格式不正确": "Captcha format is incorrect", "重新发送": "resend", "登录成功": "login is successful", "智能问答详情页": "Smart Q & A Details Page", "请输入内容": "Please enter content", "内容不能超过5000个字符": "The content cannot exceed 5000 characters", "回答输出中，请稍后操作或点击终止问答": "Answer output, please operate later or click to terminate the question and answer", "网络请求失败": "Network request failed", "服务错误": "service error", "生成合同摘要": "Generate contract summary", "通用合同": "general contract", "未匹配到审查清单, 推荐使用AI生成审查清单": "The review list is not matched. It is recommended to use AI to generate the review list", "已匹配到审查清单": "Matched to review list", "自定义审查项": "Custom review items", "生成审查清单失败": "Failed to generate review list", "高风险": "high-risk", "中风险": "medium risk", "低风险": "low-risk", "已通过": "already through the", "合同审查ID不存在": "Contract review ID does not exist", "连接已关闭，手动触发重试": "The connection has been closed, manually triggered retry", "审查中": "review", "审查成功": "examination of successful", "审查失败": "screening failure", "编辑中": "in editing", "取消删除审查记录": "Cancel deleting review records", "正在回答问题，请稍等": "Answering questions, please wait", "已禁用问答浮窗！": "Question and answer floating windows have been disabled!", "已启用问答浮窗！": "Question and answer floating window has been activated!", "无": "no", "参考条款": "terms of reference", "风险说明": "risk description", "法律依据": "legal basis", "消息不存在": "message is not present", "当前对话没有进行中的任务": "There are no ongoing tasks in the current dialogue", "获取用户信息失败": "Failed to obtain user information", "获取企业列表失败": "Failed to obtain enterprise list", "切换团队失败": "Failed to switch teams", "登录失败": "login failed", "运行中": "in the operation of", "思考已终止": "Thinking has ended", "文件解析中": "File parsing", "法律检索中": "Legal searching", "类案检索中": "Searching for category cases", "联网搜索中": "Searching online", "文章检索中": "Article searching", "思考中": "thinking", "生成中": "generation", "已完成深度思考": "Completed in-depth thinking", "运行错误": "operation error", "思考过程": "thought process", "深度思考": "deep thinking", "已阅读": "have read", "已阅读：": "Read:", "服务协议和隐私保护": "Service Agreement and Privacy Protection", "不同意": "not agree", "同意": "agreed", "标准": "standard", "对比": "contrast", "用户名称不可包含特殊字符/\\:*?<>|": "User name cannot contain special characters/\\:*? <>|", "注销账号须知": "Notes on canceling an account", "确认注销": "Confirm cancellation", "服务协议及隐私保护": "Service Agreement and Privacy Protection", "身份认证": "identity authentication", "验证": "verification", "修改用户名": "modify the username", "这是一个测试消息": "This is a test message", "发生了错误": "an error has occurred", "系统配置": "system configuration", "这是系统配置页面": "This is the system configuration page", "这是一个警告": "this is a warning", "操作完成": "operation is complete", "成功": "successful", "失败": "failure", "这是新的消息": "This is new news", "这是新的日志消息": "This is a new log message", "请注意这个警告": "Please note this warning", "配置消息": "configuration message", "这是普通的消息": "This is ordinary news", "配置标题": "configure the title", "返回结果": "return results", "断言信息": "predicate information", "普通消息": "ordinary messages", "返回值": "return value", "测试标题": "test title", "这是需要转换的消息": "This is the message that needs to be converted", "这是普通消息": "This is general news", "配置描述": "configuration description", "欢迎使用": "welcome to", "再见": "goodbye", "这是一条消息": "This is a message", "孕期被辞退，怎么维权?": "If you are dismissed during pregnancy, how to protect your rights?", "上传": "upload", "我已阅读并同意": "I have read and agree to", "账号注销协议": "Account cancellation agreement", "为了您的账号安全，请验证身份。验证成功后进行下一步操作": "For the security of your account, please verify your identity. Go to the next step after the verification is successful", "扫一扫上面二维码图案，联系我们": "Scan the QR code pattern above and contact us", "信息抽取": "information extraction", "审查项": "Review item", "请先输入审查项名称，再使用该功能自动生成规则": "Please enter the name of the review item first, and then use this function to automatically generate rules", "添加审查项": "Add review items", "审查项内容不能为空": "Review item content cannot be blank", "审查项标题不能重复": "The title of the review item cannot be duplicate", "审查类型不能重复": "Review types cannot be duplicate", "抽取": "extraction", "这是一个测试": "this is a test", "在文档中选中文字时": "When selecting text in a document", "显示快捷功能": "Display shortcut functions", "location ? location.originalText : '原文无内容'": "location ? location.originalText : 'Original text has no content'", "原文无内容": "The original text has no content", "语言": "Language", "语言切换测试": "Language Switch Test", "当前语言": "Current Language", "语言代码": "Language Code", "选择语言": "Select Language", "测试翻译": "Test Translations", "简体中文": "Simplified Chinese", "简": "<PERSON>", "繁體中文": "Traditional Chinese", "繁": "Propagation", "比对文档": "Compare documents", "比对进度": "比对进度", "比对结束后可在此查看比对结果": "You can view the comparison results here after the comparison is over", "请登录": "Please log in", "确认删除比对记录吗?": "Are you sure to delete the comparison record?", "第一个标签": "The first label", "第二个标签": "The second label", "第三个标签": "The third tab", "操作按钮": "Operation button", "搜索框名称": "Search box name", "刷新": "Refresh", "请输入搜索内容": "Please enter your search content", "我已阅读并同意 用户协议 隐私协议 和产品服务协议": "I have read and agree to the User Agreement, Privacy Agreement and Product Service Agreement", "重命名": "<PERSON><PERSON>", "编辑对话名称": "Edit conversation name", "新增角色": "New role", "原文1：": "Original 1:", "原文2：": "Original 2:", "语义影响：": "Semantic impact:", "文件加载中，请稍等...": "File loading, please wait...", "审查类型": "Review type", "添加": "Added", "请输入审查类别": "Please enter review category", "提取规则": "Extraction rules", "生成中...": "Generating...", "编辑": "Edit", "发送": "<PERSON><PERSON>", "功能权限": "Function permissions", "AI 应用平台": "AI application platform", "功能": "Function", "左:": "Left:", "右:": "Right:", "查看差异": "View differences", "查看批注": "View annotations", "语义差异": "Semantic differences", "新的测试标签": "New test label", "有差异": "There are differences", "开始比对": "Start comparing", "预计1-2分钟，请耐心等待": "Estimated 1-2 minutes, please wait patiently", "查看详情": "View details", "AI生成规则": "AI generation rules", "无差异": "No difference", "当前第": "Current first", "（部分菜单下的“数据编辑”权限点对应各类数据编辑权限，如不勾选则角色仅具备编辑自己创建数据的权限，勾选后则角色对所有自己可见的该类数据均可编辑）": "(The \"Data Edit\" permission point under some menus corresponds to various types of data editing permissions. If it is not checked, the role only has the right to edit the data it creates. After checking, the role can edit all such data visible to it)", "根据所选文本提问": "Ask questions based on the selected text", "请输入你想要了解的问题": "Please enter the question you want to know", "页/共": "Page/total", "页": "Page", "审查立场：": "Review position:", "风险(": "Risk (", "支持": "Support", "格式，文件≤": "Format, file ≤", "M 注意：不能直接修改后缀名 .doc为 .docx,否则会出错": "M Note: You cannot directly change the suffix.doc to.docx, otherwise an error will occur", "（一级导航）": "(Level 1 navigation)", "iTerms 法律人的AI智能工作台": "ITerms Legal Person's AI Smart Workstation", "请选择审查立场": "Please select a review position", "请选择审查目的": "Please select review purpose", "请选择审查清单": "Please select a review list", "请选择审查规则": "Please select review rules", "请检查审查是否正确": "Please check whether the review is correct", "文件大小不能超过": "File size cannot exceed", "审查连接已关闭，手动触发重试": "Review connection has been closed, manually triggered retry", "切换失败，请稍后再试": "Switching failed. Please try again later", "wps保存错误": "Wps save error", "合同比对": "Contract comparison", "信息提取": "Information extraction", "取 消": "Cancelled", "确 认": "Recognised", "编辑角色": "Edit role", "选择模型": "Selection model", "请输入大模型API-KEY": "Please enter the large model API-KEY", "新增模型成功": "New model successfully added", "新增模型失败": "Failed to add model", "合同摘要生成失败，请稍后再试": "Contract summary generation failed. Please try again later", "确认删除审查类型?": "Are you sure to delete the review type?", "请至少选择一个功能权限": "Please select at least one function permission", "编辑模型成功": "Editing model successfully", "请上传合同": "Please upload the contract", "文件上传中...": "File uploading...", "点击或将文件拖拽到这里上传": "Click or drag the file here to upload", "注意：不能直接修改后缀名.doc为.docx,否则会出错": "Note: You cannot directly change the suffix.doc to.docx, otherwise an error will occur", "，文件≤": ", file ≤", "基本字段抽取": "Basic field extraction", "履约信息抽取": "Performance information extraction", "正在抽取中…": "Being extracted…", "contractRef 未提供": "ContractRef not provided", "合同编号": "Contract number", "签订日期": "Signing date", "签订地点": "Place of signing", "甲方": "Party a", "乙方": "Party B", "付款方式": "Payment method", "付款时间": "Payment time", "交付时间": "Delivery time", "交付地点": "Place of delivery", "交付物品": "The delivery of items", "违约责任": "Liability for breach of contract", "争议解决方式": "Dispute resolution", "管辖法院": "Competent court", "履约类型": "Performance type", "履约标的物": "Subject matter of performance", "履约事件标准名称": "Standard name of performance event", "责任自然人姓名": "Name of responsible natural person", "具体履约截止日期": "Specific performance deadline", "履约金额": "Performance amount", "特殊履约条件": "Special performance conditions", "抽取字段不能为空！": "The extraction field cannot be empty!", "未抽取到有效信息，请检查抽取字段或文件类型是否正确！": "No valid information was extracted. Please check whether the extracted field or file type is correct!", "暂无原文内容！": "No original content!", "未找到对应文本！": "No corresponding text found!", "未能定位到原文": "Failed to locate the original text", "定位到部分原文": "Locate some original texts", "开始抽取": "Began to extract", "新增字段": "Newly added field", "请输入字段": "Please enter fields", "请填写所有字段内容": "Please fill in all fields", "返回重抽": "Return Redraw", "账号登录": "Account login", "审查合同": "Review of contract", "获取功能列表失败": "Failed to obtain function list", "确认继续？": "Confirm to continue?", "请输入组织名称": "Please enter organization name", "新增组织": "New organization", "组织重命名": "Organization rename", "新建成功": "New construction successfully", "用户账号": "User account", "用户姓名": "User name", "请输入真实姓名": "Please enter your real name", "用户角色": "User role", "请选择账号角色": "Please select account role", "归属部门": "Belonging department", "请选择归属部门": "Please select the department to which you belong", "新建人员": "New personnel", "请输入用户姓名": "Please enter user name", "请选择部门": "Please select a department", "请选择主部门": "Please select the main department", "请选择角色": "Please select a role", "4到20位大小写字母数字_-": "4 to 20 upper and lower case alphanumeric characters_-", "请输入用户账号": "Please enter your user account", "主部门和归属部门不匹配，主部门必须是归属部门中的一个。": "The primary department and the subordinate department do not match. The primary department must be one of the subordinate departments.", "编辑成功": "Edited successfully", "移入部门": "Moving in department", "移动人员": "Mobile personnel", "请选择要移入部门": "Please select the department to move into", "批量导入组织": "Batch Import Organizations", "正在导入，请等待": "Importing, please wait", "下载错误数据成功": "Error data downloaded successfully", "下载错误数据失败": "Failed to download erroneous data", "下载Excel模板，批量填写信息。": "Download the Excel template and fill in the information in batches.", "下载模板": "Download the template", "上传填写好的信息。": "Upload the completed information.", "上传模板": "Upload template", "上传完成。": "Upload complete.", "(有错误数据时，上传正确数据，下载错误数据）": "(When there is error data, upload the correct data and download the error data)", "数据正在上传,请稍后…": "Data is being uploaded, please wait…", "数据上传完毕，处理中...": "Data upload complete, processing...", "数据上传完成。": "Data upload complete.", "部分数据上传完成。": "Some data upload completed.", "下载错误数据": "Download error data", "导入失败。": "Import failed.", "部门": "Departments", "负责人": "Person in charge", "该组织下未添加人员信息": "No person information was added under this organization", "设置部门负责人": "Set up department leader", "搜索组织": "Search organization", "新建组织": "New organization", "设置负责人": "Setup leader", "取消负责人": "Cancel the person in charge", "批量导入": "Batch import", "新增用户": "New users", "搜索用户": "Search user", "用户名称": "User name", "用户账户": "User account", "设为负责人": "Set as the person in charge", "获取组织列表失败，请稍后重试": "Failed to get the organization list. Please try again later", "删除组织": "Delete an organization", "删除失败，请稍后重试": "Delete failed. Please try again later", "确定删除该用户吗？": "Are you sure you want to delete this user?", "删除确认": "Delete confirmation", "请勾选选择需要移动部门的人员": "Please check and select the person who needs to move the department", "移除负责人": "Remove the person in charge", "非上级": "Non-superior", "否": "No", "上级": "Superior", "是": "Is", "未交接": "No handover", "已交接": "Have been handed over", "禁用": "Disable", "正常": "Normal", "转岗": "Transfer", "离职": "Departure", "永久": "Permanent", "自定义": "Custom", "编辑角色成功": "Editing role successfully", "新增角色成功": "New role successfully added", "新增角色失败": "Failed to add role"}