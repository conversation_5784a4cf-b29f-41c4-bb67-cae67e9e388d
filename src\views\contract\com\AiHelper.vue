<script lang="ts" setup>
import { ChatStatusEnum, SmartChatTypeEnum } from './Model'
import { useAiHelperService } from './useAiHelperService'
import { $t } from '@/utils/i18n'
const {
  barScroller,
  resetSmartChat,
  changeSetting,
  setting,
  send,
  configVisible,
  refer,
  chatBtnStatus,
  isChating,
  refAnswer,
  questionStr,
  handleEnter,
  removeRefer,
  getOutput,
  getQuestionOutput,
  smartChats,
} = useAiHelperService()

const watchText = watch(
  () => questionStr.value,
  (str) => {
    if (str && str.trim() != '') {
      chatBtnStatus.value = ChatStatusEnum.SENDABLE
    } else {
      chatBtnStatus.value = ChatStatusEnum.DISABLE
    }
  },
  {
    immediate: true,
  },
)

defineExpose({
  setReferText(selectRang: any) {
    refer.value = selectRang.text
  },
})

onUnmounted(() => {
  watchText()
  resetSmartChat()
})
</script>
<template>
  <div class="page-wrap">
    <el-scrollbar ref="refAnswer" class="chat-wrap" style="flex: 1 auto; height: 100%" @mousewheel="barScroller">
      <div class="chat-inner" @mousewheel="barScroller">
        <div v-for="item in smartChats" :key="item.id" class="chat-node">
          <div v-if="item.type === SmartChatTypeEnum.QUESTION" class="chat-node-me">
            <div class="chat-node-me__txt" v-html="getQuestionOutput(item.content)"></div>
          </div>
          <div v-else class="chat-node-ai markdown-body">
            <div v-html="getOutput(item)"></div>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <div class="footer-wrap">
      <div v-if="refer" class="text-refer">
        <span class="text-refer-txt">{{ refer }}</span>

        <i class="iconfont icon-is-close" style="cursor: pointer" @click="removeRefer" />
      </div>
      <el-input
        ref="refInput"
        v-model="questionStr"
        :disabled="isChating"
        class="textarea-box"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 5 }"
        :placeholder="
          isChating ? $t('发送中...') : refer.trim() != '' ? $t('根据所选文本提问') : $t('请输入你想要了解的问题')
        "
        @keypress="handleEnter"
      >
      </el-input>
      <div class="send-wrap">
        <div
          class="send-wrap-config"
          tabindex="0"
          @blur="
            () => {
              configVisible = false
            }
          "
        >
          <!-- <i
            class="iconfont icon-is-setting"
            @click="
              () => {
                configVisible = !configVisible
              }
            "
            style="font-size: 18px; color: rgb(0 0 0 / 100%); cursor: pointer"
          ></i> -->

          <!-- <el-card v-if="configVisible" class="send-wrap-config-bar">
            <h3>{{ $t('问答设置') }}</h3>
            <div class="switch">
              <el-switch v-model="setting" @change="changeSetting"></el-switch>
              <span>{{ $t('问答浮窗') }}</span>
              <el-tooltip effect="light" placement="bottom-start" visible-arrow>
                <template #content> {{ $t('在文档中选中文字时') }}<br />{{ $t('显示快捷功能') }} </template>
                <icons name="warning" />
              </el-tooltip>
            </div>
          </el-card> -->
        </div>
        <!-- <el-button type="primary" :disabled="!isChating" @click="send">{{ $t('发送') }}</el-button> -->
        <div v-if="chatBtnStatus === ChatStatusEnum.DISABLE" class="input-send-btn not-allowed">
          <Icons name="send-disable" width="2rem" height="2rem" color="#aeaeae" />
        </div>
        <div v-if="chatBtnStatus === ChatStatusEnum.SENDABLE" class="input-send-btn" @click="send">
          <Icons name="send" width="2rem" height="2rem" color="red" />
        </div>
        <div v-if="chatBtnStatus === ChatStatusEnum.STOPABLE" class="input-send-btn" color="#aeaeae">
          <el-tooltip :content="$t('chat.btns.cancel')" placement="top" style="display: flex; align-items: center">
            <div>
              <Icons name="stop" width="2rem" height="2rem" />
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.not-allowed {
  cursor: not-allowed !important;
}
.input-send-btn {
  cursor: pointer;
}
.page-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 4rem);
  background-color: var(--bg-color);
  .chat-wrap {
    flex: 1;
    flex-direction: column;
    width: 100%;
    padding: 1rem;
    overflow: hidden auto;
    .chat-node {
      width: 100%;
      margin-bottom: 1rem;
      &-me {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        &__txt {
          display: inline-block;
          max-width: 80%;
          padding: 0.75rem;
          color: var(--minor-font);
          letter-spacing: 1px;
          word-wrap: break-word;
          background-color: var(--is-color-773bef1a);
          border-top-left-radius: 0.5rem;
          border-bottom-right-radius: 0.5rem;
          border-bottom-left-radius: 0.5rem;
        }
      }
    }
  }
  .footer-wrap {
    width: 100%;
    padding-top: 0.5rem;
    border-top: 1px solid var(--page-header-line);
    :deep(.el-textarea__inner) {
      font-size: 0.875rem !important;
      color: var(--minor-font) !important;
      resize: none !important;
      border: none !important;
      box-shadow: none !important;
    }
    :deep(.el-textarea.is-disabled .el-textarea__inner) {
      background-color: var(--bg-color) !important;
    }
    .send-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 3rem;
      padding: 0 1rem;
      &-config {
        position: relative;
        i {
          color: var(--sidebar-line);
          cursor: pointer;
        }
        .icon-is-config {
          font-size: 1.25rem;
          &:hover {
            color: var(--is-color-773bef);
          }
        }
        &-bar {
          position: absolute;
          top: -6.5rem;
          left: 0;
          width: 10rem;
          padding: 1rem;
          color: var(--minor-font);
          :deep(.el-card__body) {
            padding: 0 !important;
          }
          h3 {
            margin-bottom: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.5rem;
          }
          .switch {
            display: flex;
            align-items: center;
            height: 1.375rem;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.375rem;
            span {
              margin: 0 0.25rem 0 0.5rem;
            }
            .icon-is-tips {
              font-size: 1rem;
            }
          }
        }
      }
    }
    .text-refer {
      display: flex;
      align-items: center;
      height: 2.25rem;
      padding: 0 0.5rem;
      margin: 0 1rem;
      background-color: var(--input-bg);
      border-radius: 0.25rem;
      &-txt {
        flex: 1;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .el-icon-close {
        margin-left: 0.5rem;
        cursor: pointer;
      }
    }
  }
}
:deep(.el-card__body) {
  padding: 0 !important;
}
:deep(.simplebar-scrollbar) {
  width: 0 !important;
}
</style>
