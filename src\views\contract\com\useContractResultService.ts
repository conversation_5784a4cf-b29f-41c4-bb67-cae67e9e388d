import { fetch as SSE } from '@/services/sse'
import { useContractStore } from '@/stores'
import { ContractRisk, ContractRiskItem, EBusEvents } from './Model'
import mitt from '@/utils/eventsBus'
import { $t } from '@/utils/i18n'
export function useContractResultService() {
  const progress = ref(0)
  const activeLevel = ref(-1)
  const isMiniWidth = ref(false)

  const isExpandAll = ref(false)

  const contractStore = useContractStore()
  interface ILevel {
    name: string
    level: number
    color: string
    bgColor: string
    count: number
  }
  const levelOptions = ref<ILevel[]>([
    { name: $t('高风险'), level: 1, color: '#E6555E', bgColor: '#FEEEED', count: 0 },
    { name: $t('中风险'), level: 2, color: '#E0972E', bgColor: '#FFF3DD', count: 0 },
    { name: $t('低风险'), level: 3, color: '#492ED1', bgColor: '#F1F5FF', count: 0 },
    { name: $t('已通过'), level: 4, color: '#1B9275', bgColor: '#E8F6F1', count: 0 },
  ])

  const handleDropdown = () => {
    isExpandAll.value = !isExpandAll.value
    contractRisks.value.forEach((item) => {
      item.isActive = isExpandAll.value
      item.ruleItemList.forEach((ruleItem) => {
        ruleItem.isActive = isExpandAll.value
      })
    })
  }
  const contractRisks = ref<ContractRisk[]>([])

  const ctrl = ref<AbortController>()
  /**
   * level filter risk
   * @param level
   */

  const handleSelectOption = (level: ILevel) => {
    if (activeLevel.value === level.level) {
      activeLevel.value = -1
    } else {
      activeLevel.value = level.level
    }
  }

  const filterData = computed(() => {
    // 当activeLevel为-1时直接返回原始数据
    if (activeLevel.value === -1) {
      return contractRisks.value.map((item) => {
        return item.copy()
      })
    }

    // 否则进行过滤处理
    return contractRisks.value
      .map((item) => {
        const filtered = item.copy()
        filtered.ruleItemList = item.ruleItemList.filter((it) => it.ruleLevel === activeLevel.value)
        return filtered
      })
      .filter((item) => item.ruleItemList.length > 0)
      .sort((a, b) => a.orderNum - b.orderNum)
  })

  /**
   * 合同风险审查
   * @param recordId
   */
  const contractRiskReview = async () => {
    const recordId = contractStore.contractInfo.recordId
    if (!recordId) {
      // ElMessage.error('合同审查ID不存在')
      return
    }

    contractRisks.value.length = 0

    levelOptions.value.map((item) => {
      item.count = 0
      return item
    })
    ctrl.value = new AbortController()
    const url = `/llm/llm-review-record-risk/stream?recordId=${recordId}`
    SSE(
      url,
      {},
      {
        async onopen(response) {
          progress.value = 0
        },
        async onmessage(event) {
          const data = JSON.parse(event.data) as ContractRisk
          if (data) {
            progress.value = data.progress

            await assignRiskData(data)
            countLevelNum(contractRisks.value)
          }
        },
        onclose() {
          contractRisks.value = contractRisks.value.sort((a, b) => a.orderNum - b.orderNum)
          console.log('close', contractRisks.value)
        },
        onerror(err) {
          console.log(err)
          throw new Error($t('审查连接已关闭，手动触发重试'))
        },
      },
      ctrl.value,
      'GET',
    )
  }

  const assignRiskData = async (data: ContractRisk) => {
    if (!data.ruleTypeName) {
      return
    }

    let constractRisk = contractRisks.value.find((item) => item.ruleTypeName === data.ruleTypeName)
    if (!constractRisk) {
      constractRisk = new ContractRisk()
      contractRisks.value.push(constractRisk)
    }
    constractRisk.llmReviewRecordId = data.llmReviewRecordId
    constractRisk.ruleTypeName = data.ruleTypeName
    constractRisk.orderNum = data.orderNum
    constractRisk.progress = data.progress

    data.ruleItemList.forEach((item) => {
      let riskItem = constractRisk.ruleItemList.find((riskItem) => riskItem.id === item.id)

      if (!riskItem) {
        riskItem = new ContractRiskItem(item)
        constractRisk.addRuleItem(riskItem)
        const level = levelOptions.value.find((option) => option.level === riskItem!.ruleLevel)
        riskItem.setFont(level)
      }
    })
  }

  const countLevelNum = (data: ContractRisk[]) => {
    levelOptions.value.forEach((item) => {
      item.count = 0
    })
    data.forEach((item) => {
      item.ruleItemList.forEach((ruleItem) => {
        levelOptions.value[ruleItem.ruleLevel - 1].count++
      })
    })
  }

  mitt.on(EBusEvents.CONSTRACT_INFO, async (info: any) => {
    await contractRiskReview()
  })
  onBeforeUnmount(() => {
    mitt.off(EBusEvents.CONSTRACT_INFO)
  })

  const toggleItem = (risk: ContractRisk) => {
    const originalRisk = contractRisks.value.find((item) => item.ruleTypeName === risk.ruleTypeName)

    if (originalRisk) {
      originalRisk.isActive = !originalRisk.isActive
      // 触发响应式更新
      contractRisks.value = [...contractRisks.value]
    }
  }

  return {
    toggleItem,
    levelOptions,
    progress,
    activeLevel,
    isMiniWidth,
    isExpandAll,
    contractRisks,
    filterData,
    handleSelectOption,
    handleDropdown,
    contractRiskReview, // loadContractInfoById,
  }
}
