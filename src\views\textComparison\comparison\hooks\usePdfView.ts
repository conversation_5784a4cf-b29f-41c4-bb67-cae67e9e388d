// @ts-nocheck
import { useVuePdfEmbed } from 'vue-pdf-embed'
import { fabric } from 'fabric-pure-browser'
import { downloadPdfUrl } from '@/services/common'
import { BASE_API } from '@/utils/config'
import { PDF_BASE_URL } from '@/utils/config'
// import { dealBaseUrlWithSlash } from '@/utils/url';
// import { dealBaseUrlWithSlash } from '@/utils/url';

// const baseUrl = dealBaseUrlWithSlash();
const baseUrl = PDF_BASE_URL

export default function (refRightDiffs, generalData) {
  const refOriginPdf = ref([])
  const refTargetPdf = ref([])
  const originObj = reactive({ pdfUrl: '', pdfUrlAfter: '', pdfList: [], url: '', urlAfter: '' })
  const targetObj = reactive({ pdfUrl: '', pdfUrlAfter: '', pdfList: [], url: '', urlAfter: '' })

  const afterLoading = ref(true)
  const colorList = [
    { label: 'DELETE', value: '#f85951', line: 'rgb(249, 122, 115)' },
    { label: 'CHANGE', value: '#ffc600', line: '#ffc600' },
    { label: 'INSERT', value: '#1fac78', line: 'rgb(31, 172, 120)' },
  ]

  let originWrap, targetWrap, originView, targetView
  let wrapWidth, wrapHeight, originScrollTop, targetScrollTop, viewWidth

  const loadingPro = ref(0)
  function initPdfView(data) {
    originObj.pdfList = data.leftPdfDetail.map((item) => ({
      pageNo: item.pageNo,
      diffList: item.diffList || [],
      width: item.width,
      height: item.height,
      hwScale: item.height / item.width,
      scale: 0,
      render: false,
      canvas: null,
    }))
    targetObj.pdfList = data.rightPdfDetail.map((item) => ({
      pageNo: item.pageNo,
      diffList: item.diffList || [],
      width: item.width,
      height: item.height,
      hwScale: item.height / item.width,
      scale: 0,
      render: false,
      canvas: null,
    }))
    getCurrentAttr()
    const params1 = '&start=1&end=5'
    Promise.all([
      convertPdfUrl(data.leftPdfFilePath.split(BASE_API)[1] + params1),
      convertPdfUrl(data.rightPdfFilePath.split(BASE_API)[1] + params1),
    ]).then((res) => {
      originObj.pdfUrl = res[0]
      targetObj.pdfUrl = res[1]
      loadingPro.value = 100
      getPdfUrl()
    })

    if (originObj.pdfList.length <= 5 && targetObj.pdfList.length <= 5) return
    const params2 = '&start=6'
    Promise.all([
      convertPdfUrl(data.leftPdfFilePath.split(BASE_API)[1] + params2),
      convertPdfUrl(data.rightPdfFilePath.split(BASE_API)[1] + params2),
    ]).then((res) => {
      originObj.pdfUrlAfter = res[0]
      targetObj.pdfUrlAfter = res[1]
      getPdfUrlAfter()
      afterLoading.value = false
    })
  }

  function getCurrentAttr() {
    originWrap = document.getElementById('originWrap')
    targetWrap = document.getElementById('targetWrap')
    originView = document.getElementById('originView')
    targetView = document.getElementById('targetView')

    wrapWidth = originWrap.clientWidth
    wrapHeight = originWrap?.clientHeight
    originScrollTop = originView.scrollTop
    targetScrollTop = targetView.scrollTop
    viewWidth = originView.clientWidth
    originObj.pdfList.forEach((item) => {
      item.scale = viewWidth / item.width
    })
    targetObj.pdfList.forEach((item) => {
      item.scale = viewWidth / item.width
    })
    clearDraw()
  }

  function getPdfUrl() {
    const originTask = {
      scale: 2,
      url: originObj.pdfUrl,
      cMapUrl: baseUrl + '/pdf-viewer/web/cmaps/',
      cMapPacked: true,
    }
    const targetTask = {
      scale: 2,
      url: targetObj.pdfUrl,
      cMapUrl: baseUrl + '/pdf-viewer/web/cmaps/',
      cMapPacked: true,
    }
    originObj.url = originTask
    targetObj.url = targetTask
    getPdfRender('origin')
    getPdfRender('target')
    handleObserver()
    // Promise.all([originTask.promise, targetTask.promise]).then((res) => {
    //   originObj.url = originTask
    //   targetObj.url = targetTask
    //   // for (let i = 0; i < res[0].numPages; i++) {
    //   //   originObj.pdfList[i].scale = viewWidth / originObj.pdfList[i].width;
    //   // }
    //   // for (let i = 0; i < res[1].numPages; i++) {
    //   //   targetObj.pdfList[i].scale = viewWidth / targetObj.pdfList[i].width;
    //   // }
    //   nextTick(() => {
    //     getPdfRender('origin')
    //     getPdfRender('target')
    //     handleObserver()
    //   })
    // })
  }

  function getPdfUrlAfter() {
    const originTask = {
      scale: 2,
      url: originObj.pdfUrlAfter,
      cMapUrl: baseUrl + '/pdf-viewer/web/cmaps/',
      cMapPacked: true,
    }
    const targetTask = {
      scale: 2,
      url: targetObj.pdfUrlAfter,
      cMapUrl: baseUrl + '/pdf-viewer/web/cmaps/',
      cMapPacked: true,
    }
    originObj.urlAfter = originTask
    targetObj.urlAfter = targetTask
    // Promise.all([originTask.promise, targetTask.promise]).then((res) => {
    //   originObj.urlAfter = originTask
    //   targetObj.urlAfter = targetTask
    //   // for (let i = 0; i < res[0].numPages; i++) {
    //   //   originObj.pdfList[i + 5].scale = viewWidth / originObj.pdfList[i + 5].width;
    //   // }
    //   // for (let i = 0; i < res[1].numPages; i++) {
    //   //   targetObj.pdfList[i + 5].scale = viewWidth / targetObj.pdfList[i + 5].width;
    //   // }
    // })
  }

  function getPdfRender(flag) {
    // console.log('wrapHeight', wrapHeight);
    // console.log('viewscollTop', viewscollTop);
    // console.log('viewHeight', viewHeight);
    const pdfList = flag === 'origin' ? originObj.pdfList : targetObj.pdfList
    const viewscollTop = flag === 'origin' ? originView.scrollTop : targetView.scrollTop
    pdfList.forEach((pdf) => {
      // render为true 则渲染出对应pdf页
      if (pdf.render) return
      const num = pdf.pageNo
      const viewHeight = viewWidth * pdf.hwScale
      const bHeight = getBeforePageHeight(pdfList, num)
      if (bHeight + viewHeight - viewscollTop >= 0 && bHeight - viewscollTop <= wrapHeight) {
        // pdf.render = true
        drawPositon(num, flag)
      }
    })
  }

  function drawPositon(num, flag) {
    const pdfList = flag === 'origin' ? originObj.pdfList : targetObj.pdfList
    const refPdf = flag === 'origin' ? refOriginPdf : refTargetPdf
    // const postions = flag === 'origin' ? originObj.postions : targetObj.postions;
    if (pdfList[num].canvas) return
    const _canvas = document.createElement('canvas')
    refPdf.value[num].appendChild(_canvas)

    const fabricCanvas = new fabric.Canvas(_canvas, {
      width: viewWidth,
      height: viewWidth * pdfList[num].hwScale,
    })
    const positionItem = pdfList.find((item) => {
      return item.pageNo == num
    })
    if (!positionItem || !positionItem.diffList) return
    const viewScale = pdfList[num].scale
    positionItem.diffList.forEach((pos) => {
      const rect = new fabric.Rect({
        top: pos.y * viewScale,
        left: pos.x * viewScale,
        width: pos.width * viewScale,
        height: pos.height * viewScale,
        angle: 0,
        fill: getColor(pos.diffType),
        opacity: 0.5,
        hasControls: false,
        hasBorders: false,
        lockMovementX: true,
        lockMovementY: true,
        selectable: false,
      })
      fabricCanvas.add(rect)
    })
    pdfList[num].canvas = fabricCanvas
  }

  function getColor(type) {
    const color = colorList.find((item) => item.label == type)
    return color.value || ''
  }

  function handleScale(zoom) {
    const newWidth = wrapWidth * (zoom / 100) + 'px'
    originView.style.width = newWidth
    targetView.style.width = newWidth
    getCurrentAttr()
    if (viewWidth - wrapWidth) {
      const len = (viewWidth - wrapWidth) / 2
      originWrap.scrollLeft = len
      targetWrap.scrollLeft = len
    } else {
      const len = (wrapWidth - viewWidth) / 2
      originView.style.marginLeft = len + 'px'
      targetView.style.marginLeft = len + 'px'
    }
  }

  function renderScale(flag) {
    const pdfList = flag === 'origin' ? originObj.pdfList : targetObj.pdfList
    // const postions = flag === 'origin' ? originObj.postions : targetObj.postions;
    pdfList.forEach((pdf) => {
      if (pdf.canvas) {
        const hwScale = pdf.hwScale
        const viewScale = pdf.scale
        pdf.canvas.clear()
        pdf.canvas.setWidth(viewWidth)
        pdf.canvas.setHeight(viewWidth * hwScale)

        const positionItem = pdfList[pdf.pageNo]
        if (!positionItem || !positionItem.diffList) return
        positionItem.diffList.forEach((pos) => {
          const rect = new fabric.Rect({
            top: pos.y * viewScale,
            left: pos.x * viewScale,
            width: pos.width * viewScale,
            height: pos.height * viewScale,
            angle: 0,
            fill: getColor(pos.diffType),
            opacity: 0.5,
            hasControls: false,
            hasBorders: false,
            lockMovementX: true,
            lockMovementY: true,
            selectable: false,
          })
          pdf.canvas.add(rect)
        })
      }
    })
  }

  let observer
  const count = ref(0)
  function handleObserver() {
    observer = new ResizeObserver(() => {
      count.value++
      getCurrentAttr()
      renderScale('origin')
      renderScale('target')
      getPdfRender('origin')
      getPdfRender('target')
    })
    observer.observe(originView)
    resetPageIntersectionObserver()
  }

  let pageIntersectionObserver
  const resetPageIntersectionObserver = () => {
    pageIntersectionObserver?.disconnect()
    pageIntersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = refTargetPdf.value.indexOf(entry.target)
          const index2 = refOriginPdf.value.indexOf(entry.target)
          if (index > -1) {
            targetObj.pdfList[index].render = true
          } else {
            originObj.pdfList[index2].render = true
          }
        }
      })
    })
    refTargetPdf.value.forEach((element) => {
      pageIntersectionObserver.observe(element)
    })
    refOriginPdf.value.forEach((element) => {
      pageIntersectionObserver.observe(element)
    })
  }

  onBeforeUnmount(() => {
    observer && observer.disconnect()
  })

  //  =============定位相关========================

  let forbidAlign = true // 手动滚时两边要同步，定位时不要同步滚
  let originFlag = true // 标记是在左还是在右滚动
  let scrollLen = 0
  // const generalData = inject('generalData')

  function handleWheel(e) {
    const clientX = e.clientX - e.offsetX
    originFlag = clientX - 10 < wrapWidth
    forbidAlign = false
    clearDraw()
  }

  function handleScrollPdf(e, flag, obj) {
    const sTop = originFlag ? originScrollTop : targetScrollTop
    scrollLen = e.target.scrollTop - sTop

    if (flag === 'origin') {
      originScrollTop = e.target.scrollTop
      obj.headerShadow = originScrollTop !== 0
      for (let i = originObj.pdfList.length - 1; i >= 0; i--) {
        const sum = getBeforePageHeight(originObj.pdfList, i)
        const curHeight = viewWidth * originObj.pdfList[i].hwScale
        if (curHeight - (originScrollTop - sum) + 200 < wrapHeight) {
          obj.curPage = i + 2
          break
        } else if (originScrollTop < 200) {
          obj.curPage = 1
          break
        }
      }
    } else {
      targetScrollTop = e.target.scrollTop
      obj.headerShadow = targetScrollTop !== 0
      for (let i = targetObj.pdfList.length - 1; i >= 0; i--) {
        const sum = getBeforePageHeight(targetObj.pdfList, i)
        const curHeight = viewWidth * targetObj.pdfList[i].hwScale
        if (curHeight - (targetScrollTop - sum) + 200 < wrapHeight) {
          obj.curPage = i + 2
          break
        } else if (targetScrollTop < 200) {
          obj.curPage = 1
          break
        }
      }
    }
    getPdfRender(flag)
    if (forbidAlign || !generalData.value.isAlign) return
    if ((flag === 'origin') !== originFlag) return
    handleAlign(flag, e.target.scrollTop)
  }

  function handleScrollWrap(e, flag) {
    if (!generalData.value.isAlign) return
    const wrap = flag === 'origin' ? targetWrap : originWrap
    wrap.scrollLeft = e.target.scrollLeft
  }

  function handleAlign(flag, scrollTop) {
    // const equalList = generalData.value.diffs.filter((item) => item.diffType === 'EQUAL');
    const diffs = generalData.value.diffs
    const otherList = generalData.value.diffs.filter((item) => item.diffType !== 'EQUAL')
    let aY, bY
    if (flag === 'origin') {
      const obj = diffs.find((item) => {
        const bHeight = getBeforePageHeight(originObj.pdfList, item.leftPageNumber)
        aY = item.leftY * originObj.pdfList[item.leftPageNumber].scale + bHeight
        return Math.abs(scrollTop - aY) < 2
      })
      if (obj) {
        if (obj.diffType === 'EQUAL') {
          // const bHeight = getBeforePageHeight(originObj.pdfList, obj.leftPageNumber);
          // bY = obj.leftY * originObj.pdfList[obj.leftPageNumber].scale + bHeight;
          const bHeight = getBeforePageHeight(targetObj.pdfList, obj.rightPageNumber)
          bY = obj.rightY * targetObj.pdfList[obj.rightPageNumber].scale + bHeight
          targetView.scrollTop = bY
        } else {
          refRightDiffs.value.selectItem(obj)
        }
      } else {
        targetView.scrollTop = targetView.scrollTop + scrollLen
        if (targetView.scrollTop == 0) otherList.length && refRightDiffs.value.selectItem(otherList[0])
        if (targetView.scrollTop == targetView.scrollHeight)
          otherList.length && refRightDiffs.value.selectItem(otherList[otherList.length - 1])
      }
    } else {
      const obj = diffs.find((item) => {
        const bHeight = getBeforePageHeight(targetObj.pdfList, item.rightPageNumber)
        aY = item.rightY * targetObj.pdfList[item.rightPageNumber].scale + bHeight
        return Math.abs(scrollTop - aY) < 2
      })
      if (obj) {
        if (obj.diffType === 'EQUAL') {
          // const bHeight = getBeforePageHeight(targetObj.pdfList, obj.rightPageNumber);
          // bY = obj.rightY * targetObj.pdfList[obj.rightPageNumber].scale + bHeight;
          const bHeight = getBeforePageHeight(originObj.pdfList, obj.leftPageNumber)
          bY = obj.leftY * originObj.pdfList[obj.leftPageNumber].scale + bHeight
          originView.scrollTop = bY
        } else {
          refRightDiffs.value.selectItem(obj)
        }
      } else {
        originView.scrollTop = originView.scrollTop + scrollLen
      }
    }
  }

  function getBeforePageHeight(list, i) {
    let sum = 0
    for (let j = 0; j < i; j++) {
      // 后面的1是补齐border-bottom:1px
      sum += viewWidth * list[j].hwScale + 1
    }
    return sum
  }

  // ==========划线相关=========

  const refLine = ref()
  const showDiffLine = ref(false)
  const showNotation = ref(false)

  function handleDiff(obj) {
    forbidAlign = true
    const { leftX, leftY, leftPageNumber, rightX, rightY, rightPageNumber, diffType } = obj
    const originBH = getBeforePageHeight(originObj.pdfList, leftPageNumber)
    const originY = leftY * originObj.pdfList[leftPageNumber].scale + originBH
    if (originY < 100) originView.scrollTop = 0
    originView.scrollTop = originY - 100
    const originActualY = originY - originView.scrollTop
    let originActualX = leftX * originObj.pdfList[leftPageNumber].scale - originWrap.scrollLeft
    if (originActualX < 0) originActualX = 0

    const targetBH = getBeforePageHeight(targetObj.pdfList, rightPageNumber)
    const targetY = rightY * targetObj.pdfList[rightPageNumber].scale + targetBH
    if (targetY < 100) targetView.scrollTop = 0
    targetView.scrollTop = targetY - 100
    const targetActualY = targetY - targetView.scrollTop
    let targetActualX = rightX * targetObj.pdfList[rightPageNumber].scale - targetWrap.scrollLeft
    if (targetActualX < 0) targetActualX = 0
    drawLine(originActualX, originActualY, targetActualX, targetActualY, diffType)
  }

  function drawLine(leftX, leftY, rightX, rightY, diffType) {
    showDiffLine.value = true
    const linePath = `M${leftX} ${leftY} L${rightX + wrapWidth + 10} ${rightY}`
    nextTick(() => {
      const color = colorList.find((item) => item.label == diffType)
      refLine.value.style.stroke = color.line
      refLine.value.setAttribute('d', linePath)
    })
  }

  // ========画批注=======

  const refNotation = ref()
  const notationObj = ref({})
  const isTopArrow = ref(false)

  function drawNotation(pointObj) {
    showNotation.value = true
    showDiffLine.value = false
    const headerH = generalData.value.isFullScreen ? 0 : 60
    notationObj.value = {
      name: pointObj.titlePopup,
      time: pointObj.modifiedDate,
      content: pointObj.contents,
    }
    const pageNo = pointObj.pageIndex
    const positions = pointObj.positions
    const pointX = positions[0].x
    const pointY = positions[0].y

    const originBH = getBeforePageHeight(originObj.pdfList, pageNo)

    let originY = pointY * originObj.pdfList[pageNo].scale
    let originLen = 0
    if (originY > originWrap.offsetHeight - 100) {
      originLen = originY - originWrap.offsetHeight + 100
    }
    originView.scrollTop = originBH + originLen
    originY = originY - originLen

    let originX = pointX * originObj.pdfList[pageNo].scale - originWrap.scrollLeft
    originX = originX > 0 ? originX : 0

    if (originY < 200) {
      refNotation.value.$el.style.top = originY + 45 + headerH + 'px'
      isTopArrow.value = true
    } else {
      let h = refNotation.value.$el.offsetHeight
      if (!h) {
        nextTick(() => {
          h = refNotation.value.$el.offsetHeight
          refNotation.value.$el.style.top = originY - h + headerH + 'px'
        })
      } else {
        refNotation.value.$el.style.top = originY - h + headerH + 'px'
      }
      isTopArrow.value = false
    }
    refNotation.value.$el.style.left = originX + 'px'

    clearPositions()
    drawTheNota(positions, pageNo)
  }
  function drawTheNota(positions, pageNo) {
    const fabricCanvas = originObj.pdfList[pageNo].canvas
    if (fabricCanvas) {
      const viewScale = originObj.pdfList[pageNo].scale
      positions.forEach((pos) => {
        const rect = new fabric.Rect({
          type: 'notation',
          top: pos.y * viewScale,
          left: pos.x * viewScale,
          width: pos.width * viewScale,
          height: pos.height * viewScale,
          angle: 0,
          fill: '#1E90FF',
          opacity: 0.5,
          hasControls: false,
          hasBorders: false,
          lockMovementX: true,
          lockMovementY: true,
          selectable: false,
        })
        fabricCanvas.add(rect)
      })
      originObj.pdfList[pageNo].isNotation = true
    } else {
      setTimeout(() => {
        drawTheNota(positions, pageNo)
      }, 150)
    }
  }

  function clearPositions() {
    originObj.pdfList.forEach((item) => {
      if (item.isNotation) {
        const fabricCanvas = item.canvas
        const list = fabricCanvas.getObjects()
        const rectList = list.filter(function (v) {
          return v.type == 'notation'
        })
        rectList.map(function (v) {
          fabricCanvas.remove(v)
        })
        item.isNotation = false
      }
    })
  }

  function clearDraw() {
    showDiffLine.value = false
    showNotation.value = false
    clearPositions()
  }

  function toTop() {
    originView.scrollTop = targetView.scrollTop = 0
    clearDraw()
  }

  // =========点击坐标点定位============
  function handleMouseClick(e) {
    const clientX = e.clientX
    const clientY = e.clientY
    const headerH = generalData.value.isFullScreen ? 0 : 69
    const isLeft = clientX - wrapWidth <= 0
    let scrollLeft = 0
    let scrollTop = 0
    let pdfList = []
    const diffList = []
    if (isLeft) {
      scrollLeft = originWrap.scrollLeft
      scrollTop = originView.scrollTop
      pdfList = originObj.pdfList
      originObj.pdfList.forEach((item) => {
        diffList.push(...item.diffList)
      })
    } else {
      scrollLeft = targetWrap.scrollLeft
      scrollTop = targetView.scrollTop
      pdfList = targetObj.pdfList
      targetObj.pdfList.forEach((item) => {
        diffList.push(...item.diffList)
      })
    }

    const pos = diffList.find((item) => {
      const originBH = getBeforePageHeight(pdfList, item.pageNo)
      const scale = pdfList[item.pageNo].scale
      const y = item.y * scale + originBH - scrollTop + headerH
      const h = item.height * scale
      let x = 0
      if (isLeft) {
        x = item.x * scale - scrollLeft + 10
      } else {
        x = item.x * scale - scrollLeft + wrapWidth + 20
      }

      const w = item.width * scale
      if (clientY >= y - 5 && clientY <= y + h + 5 && clientX >= x - 5 && clientX <= x + w + 5) return true
    })
    if (pos) {
      refRightDiffs.value.selectDiff(pos)
    }

    // console.log('isLeft', isLeft);
    // console.log('diffList', diffList);
  }

  return {
    initPdfView,
    refOriginPdf,
    originObj,
    refTargetPdf,
    targetObj,

    handleScale,

    showDiffLine,
    handleDiff,
    handleScrollPdf,
    handleWheel,
    handleScrollWrap,
    refLine,

    showNotation,
    refNotation,
    notationObj,
    isTopArrow,
    drawNotation,
    toTop,
    loadingPro,
    afterLoading,
    handleMouseClick,
    count,
  }
}

async function convertPdfUrl(url) {
  const { data } = await downloadPdfUrl(url)
  const blob = new Blob([data], {
    type: 'application/pdf',
  })
  return URL.createObjectURL(blob)
}
