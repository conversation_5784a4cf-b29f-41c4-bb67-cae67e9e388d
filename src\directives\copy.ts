import type { Directive, DirectiveBinding } from 'vue'

/**
 * 复制到剪贴板指令
 * 用法：
 * v-copy="textToCopy" - 点击元素时复制指定文本
 * v-copy.success="handleSuccess" - 复制成功回调
 * v-copy.error="handleError" - 复制失败回调
 * v-copy:element - 复制元素的文本内容
 */
const copyDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const handler = async (event: Event) => {
      try {
        let textToCopy = ''

        if (binding.arg === 'element') {
          // 复制元素的文本内容
          textToCopy = el.textContent || el.innerText || ''
        } else if (typeof binding.value === 'string') {
          // 复制指定的文本
          textToCopy = binding.value
        } else if (binding.value && typeof binding.value.text === 'string') {
          // 复制配置对象中的文本
          textToCopy = binding.value.text
        } else {
          console.warn('v-copy: No text to copy specified')
          return
        }

        // 使用现代 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(textToCopy)
        } else {
          // 降级到传统方法
          await fallbackCopyTextToClipboard(textToCopy)
        }

        // 触发成功回调
        if (binding.value && typeof binding.value.onSuccess === 'function') {
          binding.value.onSuccess(textToCopy)
        }

        // 显示成功提示
        showCopyFeedback(el, 'success')

      } catch (error) {
        console.error('Copy failed:', error)
        
        // 触发错误回调
        if (binding.value && typeof binding.value.onError === 'function') {
          binding.value.onError(error)
        }

        // 显示错误提示
        showCopyFeedback(el, 'error')
      }
    }

    // 保存处理函数引用
    ;(el as any).__copyHandler = handler
    
    // 添加点击事件监听器
    el.addEventListener('click', handler)
    
    // 添加样式提示这是可复制的
    el.style.cursor = 'pointer'
    el.title = el.title || '点击复制'
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 更新提示文本
    if (binding.arg === 'element') {
      el.title = '点击复制元素内容'
    } else if (typeof binding.value === 'string') {
      el.title = `点击复制: ${binding.value.substring(0, 20)}${binding.value.length > 20 ? '...' : ''}`
    }
  },

  beforeUnmount(el: HTMLElement) {
    const handler = (el as any).__copyHandler
    if (handler) {
      el.removeEventListener('click', handler)
      delete (el as any).__copyHandler
    }
  }
}

/**
 * 降级复制方法（兼容旧浏览器）
 */
function fallbackCopyTextToClipboard(text: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)
      
      if (successful) {
        resolve()
      } else {
        reject(new Error('Copy command failed'))
      }
    } catch (err) {
      document.body.removeChild(textArea)
      reject(err)
    }
  })
}

/**
 * 显示复制反馈
 */
function showCopyFeedback(el: HTMLElement, type: 'success' | 'error') {
  const feedback = document.createElement('div')
  feedback.textContent = type === 'success' ? '复制成功!' : '复制失败!'
  feedback.style.cssText = `
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: ${type === 'success' ? '#67c23a' : '#f56c6c'};
    color: white;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  `

  // 确保父元素有相对定位
  if (getComputedStyle(el).position === 'static') {
    el.style.position = 'relative'
  }

  el.appendChild(feedback)

  // 显示动画
  setTimeout(() => {
    feedback.style.opacity = '1'
  }, 10)

  // 自动移除
  setTimeout(() => {
    feedback.style.opacity = '0'
    setTimeout(() => {
      if (el.contains(feedback)) {
        el.removeChild(feedback)
      }
    }, 300)
  }, 2000)
}

export default copyDirective
