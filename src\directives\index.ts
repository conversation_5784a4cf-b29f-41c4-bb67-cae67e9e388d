import type { App } from 'vue'
import authDirective from './auth'
import loadingDirective from './loading'
import clickOutsideDirective from './click-outside'
import copyDirective from './copy'

/**
 * 注册全局自定义指令
 */
export function setupDirectives(app: App) {
  // 权限控制指令
  app.directive('auth', authDirective)

  // 加载状态指令
  app.directive('loading', loadingDirective)

  // 点击外部区域指令
  app.directive('click-outside', clickOutsideDirective)

  // 复制到剪贴板指令
  app.directive('copy', copyDirective)
}

export default setupDirectives
