<script lang="ts" setup>
import Header from '@/layout/components/Header.vue'
import draftInfo from '@/assets/images/draft-info.svg'
import reviewInfo from '@/assets/images/review-info.svg'
import router from '@/router'

import { ref } from 'vue'
import CreateDraftDialog from './com/CreateDraftDialog.vue'
import Uploader from './com/Uploader.vue'
import RecentContracts from './com/RecentContracts.vue'
import { useUserStore } from '@/stores'
const draftDialog = ref()
const userStore = useUserStore()
const showDraftDialog = () => {
  if (userStore.token) {
    draftDialog.value.open()
  } else {
    userStore.setShowLoginBox(true)
  }
}

const gotoReview = (data: any) => {
  // reviewStore.contractInfo
  // .setId(data.contractId)
  // .setRecordId(data.contractId)
  // .setIsReviewed(false)
  // .setContractName(data.fileName)
  // .setContractUrl(data.fileCode)
  router.push(`/contract/view/1/` + data.contractId)
}
const goto = () => {
  console.log(123)

  history.back()
}
</script>
<template>
  <div class="review-wrap" style="flex-direction: column; padding: 0.75rem 1rem">
    <Header :title="$t('合同')" :show-back="false"></Header>
    <div style="display: flex; flex-direction: row; gap: 1rem">
      <div class="draft-wrap" style="position: relative">
        <div
          class="draft-box"
          :style="{
            backgroundImage: `url(https://cdn.fadada.com/dist/static/c/39/20250616225718_c8e00b08-cc77-4792-aefa-0548b8992c19.svg)`,
          }"
        >
          <div class="c-header">{{ $t('合同起草') }}</div>
          <div class="c-info">{{ $t('输入合同信息，AI辅助生成，高效又省心') }}</div>
        </div>
        <div class="radius-btn" style="position: absolute; bottom: 3.5rem; left: 2rem" @click="showDraftDialog">
          {{ $t('开始起草') }}
        </div>
        <div style="position: absolute; right: 0; bottom: 1.25rem">
          <img :src="draftInfo" />
        </div>
      </div>
      <div class="upload-wrap" style="position: relative">
        <div
          class="upload-inner"
          :style="{
            backgroundImage: `url(https://cdn.fadada.com/dist/static/c/39/20250616225841_bba1fa7b-8be3-42f2-8f45-50934961f3da.svg)`,
          }"
        >
          <div class="c-header">{{ $t('合同审查') }}</div>
          <div class="c-info">{{ $t('上传合同文件，AI审查，高效识别潜在风险') }}</div>
          <div style="position: absolute; right: 0; bottom: 1.25rem">
            <img :src="reviewInfo" />
          </div>
          <Uploader v-if="userStore.token" style="position: absolute; bottom: 2.875rem; left: 2rem">
            <div class="radius-btn">{{ $t('上传合同') }}</div>
          </Uploader>
          <div
            v-else
            class="radius-btn"
            style="position: absolute; bottom: 2.875rem; left: 2rem"
            @click="
              () => {
                userStore.setShowLoginBox(true)
              }
            "
          >
            {{ $t('上传合同') }}
          </div>
        </div>
      </div>
    </div>
    <RecentContracts />
    <CreateDraftDialog ref="draftDialog" />
  </div>
</template>

<style lang="scss" scoped>
.radius-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 7.5rem;
  height: 2.25rem;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--is-color-773bef);
  cursor: pointer;
  background-color: var(--primary-btn-bg);
  border-radius: 5rem;
}
.draft-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  min-width: 26rem;
  height: 19.125rem;
  padding: 1.5rem 0;
  background-color: var(--bg-color);
  .draft-box {
    width: 100%;
    height: 16.125rem;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: cover;
  }
}
.upload-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 70%;
  min-width: 26rem;
  padding: 1.5rem 0;
  .upload-inner {
    width: 100%;
    height: 16.125rem;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: cover;
    border-radius: 1rem;
  }
}

// .review-wrap {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
//   height: 100%;
//   padding: 10px 24px;
// }
.c-header {
  margin: 2rem 0 0 2rem;
  font-size: 1.375rem;
  font-weight: 600;
  line-height: 32.31px;
  vertical-align: middle;
  color: var(--minor-font);
  letter-spacing: 0%;
}
.c-info {
  margin: 0.75rem 0 0 2rem;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.375rem;
  color: var(--is-color-7d7b89);
  letter-spacing: 0;
}
</style>
