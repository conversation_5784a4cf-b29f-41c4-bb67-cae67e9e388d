{"name": "iterms-saas-web-v2", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "dev:pro": "vite --mode pro.development", "dev:local": "vite --mode local.development", "build": "echo \"no build\"", "build:pnpm": "vue-tsc -b && vite build", "build:test": "vite build", "preview": "vite preview", "prepare": "husky install", "t": "node src/lang/translate.js", "i18n2Key": "node src/lang/scripts/i18n-converter.js"}, "engines": {"node": ">=20.19.0"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iterms/online-word": "^1.0.4", "@iterms/pdf-viewer": "^1.0.7", "@iterms/pdfjs": "^1.0.3", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "fabric-pure-browser": "^5.1.0", "jsencrypt": "^3.3.2", "json5": "^2.2.3", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-i18n": "^11.1.9", "vue-pdf-embed": "^2.1.2", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.0", "@types/glob": "^9.0.0", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/nprogress": "^0.2.3", "@types/postcss-pxtorem": "^6.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "code-inspector-plugin": "^0.20.15", "commitlint": "^19.8.1", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.2.0", "glob": "^11.0.3", "husky": "^9.1.7", "mockjs": "^1.1.0", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-pxtorem": "^6.1.0", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard-scss": "^15.0.1", "stylelint-order": "^7.0.0", "stylelint-prettier": "^5.0.3", "ts-node": "^10.9.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-static-copy": "^3.1.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.10"}}