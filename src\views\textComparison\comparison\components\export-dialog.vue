<script setup>
import { downloadDiff } from '@/services/textComparison'
import { useRoute } from 'vue-router'
// import { useDownloadFile } from '@/composables/useDownloadFile'
import { exportStream } from '@/utils'

const visible = ref(false)
const btnLoading = ref(false)
const flagDiff = ref(false)
const flagNotation = ref(false)

const route = useRoute()
const compareId = route.query.compareId

function open() {
  visible.value = true
}

function close() {
  flagDiff.value = false
  flagNotation.value = false
  visible.value = false
}

async function downloadResult() {
  if (flagDiff.value) {
    const res = await downloadDiff({
      convertExcel: true,
      key: compareId,
    })
    exportStream(res)
  }
  if (flagNotation.value) {
    const res = await downloadDiff({
      key: compareId,
    })
    exportStream(res)
  }
  close()
}

defineExpose({
  open,
})
</script>

<template>
  <el-dialog :title="$t('导出差异结果')" v-model="visible" :close-on-click-modal="false" width="500px" @close="close">
    <div class="item-wrap">
      <el-checkbox v-model="flagDiff"></el-checkbox>
      <div class="item-text">
        <span>{{ $t('导出差异结果') }}</span>
        <span class="item-text-sub">{{ $t('只导出右侧比对差异结果，格式为excel') }}</span>
      </div>
    </div>
    <div class="item-wrap">
      <el-checkbox v-model="flagNotation"></el-checkbox>
      <div class="item-text">
        <span>{{ $t('导出批注版本') }}</span>
        <span class="item-text-sub">{{ $t('把差异结果以批注插入到左侧合同中') }}</span>
      </div>
    </div>
    <template v-slot:footer>
      <div>
        <el-button @click="close">{{ $t('取消') }}</el-button>
        <el-button
          type="primary"
          :loading="btnLoading"
          :disabled="!flagDiff && !flagNotation"
          @click="downloadResult"
          >{{ $t('确定') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.item-wrap {
  display: flex;
  .item-text {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    &-sub {
      margin-top: 5px;
      opacity: 0.8;
    }
  }
  &:first-child {
    margin-bottom: 20px;
  }
}
</style>
