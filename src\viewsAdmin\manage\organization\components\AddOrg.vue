<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="480px"
    :show-close="true"
    @close="closeDialog"
  >
    <el-form
      :model="form"
      ref="ruleForm"
      :rules="rules"
      :label-position="'left'"
      label-width="10px"
      class="demo-ruleForm custom-dialog-form"
      @submit.prevent
    >
      <el-form-item label=" " prop="orgName">
        <el-input v-model.trim="form.orgName" :placeholder="$t('请输入组织名称')" :maxlength="50"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click.stop="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" :disabled="form.orgName.length == 0" :loading="loading" @click.stop="submit">{{
          $t('确定')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { addOrgRequest, editOrgRequest } from '@/services/manage/index'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { $t } from '@/utils/i18n'
import type { FormInstance } from 'element-plus'

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const dialogTitle = ref($t('新增组织'))
const currentNode = ref<any>({})

const form = reactive({
  orgName: '',
})

const rules = {
  orgName: [{ required: true, message: $t('请输入组织名称'), trigger: 'change' }],
}

// 模板引用
const ruleForm = ref<FormInstance>()

// 定义emits
const emit = defineEmits(['succ'])

// 方法
const closeDialog = () => {
  visible.value = false
  loading.value = false
  currentNode.value = {}
  nextTick(() => {
    ruleForm.value?.resetFields()
  })
}

const openDialog = (data: any = {}) => {
  currentNode.value = Object.assign({}, data)
  form.orgName = data.orgName || ''

  dialogTitle.value = currentNode.value.orgId ? $t('组织重命名') : $t('新增组织')

  visible.value = true
}

const submit = () => {
  if (loading.value) return false
  loading.value = true
  ruleForm.value?.validate((valid) => {
    if (valid) {
      if (currentNode.value.orgId) editOrgHandle()
      else addOrgHandle()
    } else {
      loading.value = false
    }
  })
}

const addOrgHandle = async () => {
  const data = {
    parentId: currentNode.value.parentId,
    rootFlag: currentNode.value.rootFlag,
    orgName: form.orgName,
  }
  try {
    const result = await addOrgRequest(data)
    if (result.code == RESPONSE_CODE_SUCCESS) {
      ElMessage.success($t('新建成功'))
      emit('succ')
      closeDialog()
    }
  } catch (error: any) {
    ElMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

const editOrgHandle = async () => {
  const data = {
    orgId: currentNode.value.orgId,
    orgName: form.orgName,
    parentId: currentNode.value.parentId,
    rootFlag: currentNode.value.rootFlag,
  }
  loading.value = true
  try {
    const result = await editOrgRequest(data)
    if (result.code == RESPONSE_CODE_SUCCESS) {
      ElMessage.success($t('修改成功'))
      emit('succ')
      closeDialog()
    }
  } catch (error: any) {
    ElMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件使用
defineExpose({
  openDialog,
})
</script>

<style lang="scss" scoped>
.custom-dialog-form {
  :deep(.el-form-item__label) {
    display: none;
  }
}
</style>
