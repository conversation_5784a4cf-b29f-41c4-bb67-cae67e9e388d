<script setup>
import VuePdfEmbed from 'vue-pdf-embed'

import usePdfView from './hooks/usePdfView'
import viewHeader from './components/view-header.vue'
import bottomTool from './components/bottom-tool.vue'
import rightDiffs from './components/right-diffs.vue'
import notationBox from './components/notation-box.vue'
import { getPdfCompare } from '@/services/textComparison'
import { useRoute } from 'vue-router'

// import { generalDataKey } from './types/index.ts'

const route = useRoute()
const compareId = route.query.compareId
const originData = ref({})
const targetData = ref({})
const generalData = ref({
  isAlign: true,
  similarity: 0,
  isFullScreen: false,
  isShowTop: true,
  diffs: null,
})

provide('generalData', generalData)
const refRightDiffs = ref()

const {
  initPdfView,
  refOriginPdf,
  originObj,
  refTargetPdf,
  targetObj,

  handleScale,

  showDiffLine,
  handleDiff,
  handleScrollPdf,
  handleWheel,
  handleScrollWrap,
  refLine,

  showNotation,
  refNotation,
  notationObj,
  isTopArrow,
  drawNotation,
  toTop,
  loadingPro,
  afterLoading,
  handleMouseClick,
  count,
} = usePdfView(refRightDiffs, generalData)

const semanticData = ref(null)

let timer = null

async function getData() {
  const { data } = await getPdfCompare({
    key: compareId,
  })
  loadingPro.value = +data.progress < 100 ? data.progress : 99
  if (data.progress == 100) {
    originData.value = {
      headerShadow: false,
      filename: data.leftFilename,
      pageTotal: data.leftPageTotal,
      curPage: 1,
      isOrigin: true,
    }
    targetData.value = {
      headerShadow: false,
      filename: data.rightFilename,
      pageTotal: data.rightPageTotal,
      curPage: 1,
      isOrigin: false,
    }
    generalData.value.similarity = data.similarity
    generalData.value.diffs = data.result
    generalData.value.annos = data.leftAnnotations
    semanticData.value = {
      leftFilePath: data.leftFilePath,
      leftFilename: data.leftFilename,
      rightFilePath: data.rightFilePath,
      rightFilename: data.rightFilename,
      compareId,
    }
    initPdfView(data)
  } else {
    timer = setTimeout(() => {
      getData()
    }, 1000)
  }
}

function showPageTop(val) {
  generalData.value.isShowTop = val
}

onMounted(() => {
  if (!compareId) return
  getData()
})
onBeforeMount(() => {
  timer && clearTimeout(timer)
})
</script>

<template>
  <div v-loading="loadingPro != 100" class="page-wrap" :element-loading-text="`${loadingPro}%`">
    <div class="left-wrap">
      <div
        v-if="generalData.isFullScreen"
        class="left-page-top"
        @mouseenter="showPageTop(true)"
        @mouseout="showPageTop(false)"
      ></div>
      <div class="view-container" @wheel="handleWheel" @click="handleMouseClick">
        <div class="view-out">
          <viewHeader :header-obj="originData" />
          <div id="originWrap" class="view-wrap view-origin" @scroll="($e) => handleScrollWrap($e, 'origin')">
            <div id="originView" class="view-inner" @scroll="($e) => handleScrollPdf($e, 'origin', originData)">
              <div
                v-for="pdf in originObj.pdfList"
                :key="pdf.pageNo"
                ref="refOriginPdf"
                class="pdf-wrap pdf-origin"
                :style="{ '--scale': pdf.hwScale }"
              >
                <template v-if="pdf.render">
                  <VuePdfEmbed
                    :key="pdf.pageNo + count"
                    v-if="pdf.pageNo < 5"
                    :source="originObj.url"
                    :page="pdf.pageNo + 1"
                  />
                  <VuePdfEmbed
                    :key="pdf.pageNo + 'right' + count"
                    v-else
                    :source="originObj.urlAfter"
                    :page="pdf.pageNo - 4"
                  />
                </template>
                <div
                  v-else
                  v-loading="true"
                  class="after-loading"
                  :element-loading-:text="$t('文件加载中，请稍等...')"
                ></div>
              </div>
            </div>
          </div>
          <div class="page-nums-wrap">
            {{ $t('当前第') }}<span class="page-nums">{{ originData.curPage }}</span
            >{{ $t('页/共') }}{{ originData.pageTotal }}{{ $t('页') }}
          </div>
        </div>
        <div class="view-out">
          <viewHeader :header-obj="targetData" />
          <div id="targetWrap" class="view-wrap view-target" @scroll="($e) => handleScrollWrap($e, 'target')">
            <div id="targetView" class="view-inner" @scroll="($e) => handleScrollPdf($e, 'target', targetData)">
              <div
                v-for="pdf in targetObj.pdfList"
                :key="pdf.pageNo"
                ref="refTargetPdf"
                class="pdf-wrap pdf-target"
                :style="{ '--scale': pdf.hwScale }"
              >
                <template v-if="pdf.render">
                  <VuePdfEmbed
                    :key="pdf.pageNo + count"
                    v-if="pdf.pageNo < 5"
                    :source="targetObj.url"
                    :page="pdf.pageNo + 1"
                  />
                  <VuePdfEmbed
                    :key="pdf.pageNo + 'right' + count"
                    v-else
                    :source="targetObj.urlAfter"
                    :page="pdf.pageNo - 4"
                  />
                </template>
                <div
                  v-else
                  v-loading="true"
                  class="after-loading"
                  :element-loading-:text="$t('文件加载中，请稍等...')"
                ></div>
              </div>
            </div>
          </div>
          <div class="page-nums-wrap">
            {{ $t('当前第') }}<span class="page-nums">{{ targetData.curPage }}</span
            >{{ $t('页/共') }}{{ targetData.pageTotal }}{{ $t('页') }}
          </div>
        </div>
        <svg v-if="showDiffLine" class="diff-mask" :class="{ 'diff-mask-full': generalData.isFullScreen }">
          <path ref="refLine" d="M100 100 L500 500" class="diff-line"></path>
        </svg>
        <notationBox v-show="showNotation" ref="refNotation" :notation-obj="notationObj" :is-top-arrow="isTopArrow" />
      </div>
      <bottomTool @handleScale="handleScale" />
      <div class="top-icon" @click="toTop">
        <i class="icon-is-zhiding"></i>
      </div>
    </div>
    <rightDiffs
      v-if="generalData.diffs"
      ref="refRightDiffs"
      :diff-list="generalData.diffs"
      :notation-list="generalData.annos"
      :semantic-data="semanticData"
      @drawDiffLine="handleDiff"
      @drawNotation="drawNotation"
    />
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: #f0f2f5;
  .left-wrap {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 0;
    .left-page-top {
      position: absolute;
      top: 0;
      z-index: 2;
      width: 100%;
      height: 75px;
    }
    .view-container {
      position: relative;
      display: flex;
      flex: 1;
      justify-content: space-between;
      height: 0;
      padding: 10px;
      .view-out {
        display: flex;
        flex-direction: column;
        width: calc(50% - 5px);
        .page-nums-wrap {
          width: 100%;
          font-size: 12px;
          line-height: 24px;
          color: #929292;
          text-align: center;
          background-color: var(--bg-color);
          .page-nums {
            margin: 0 5px;
          }
        }
      }
      .view-wrap {
        position: relative;
        width: 100%;
        height: 100%;
        overflow-x: auto;
        background-color: var(--bg-color);
        .view-inner {
          height: 100%;
          overflow: hidden auto;
          .pdf-wrap {
            position: relative;
            width: 100%;
            height: 0;
            border-bottom: 1px solid gray;
            .after-loading {
              position: absolute;
              top: 0;
              left: 0;
              z-index: 1;
              width: 100%;
              height: 100%;
              padding-bottom: calc(100% * var(--scale));
              :deep(.el-loading-spinner .path) {
                stroke: #ccc;
              }
              :deep(.el-loading-text) {
                color: #ccc;
              }
            }
          }
          .pdf-origin {
            padding-bottom: calc(100% * var(--scale));
          }
          .pdf-target {
            padding-bottom: calc(100% * var(--scale));
          }
        }
      }
      .diff-mask {
        position: absolute;
        top: 69px;
        left: 10px;
        width: calc(100% - 20px);
        height: calc(100% - 69px);
        .diff-line {
          fill: none;

          // stroke: rgb(31, 172, 120);
          stroke-width: 2;
          stroke-dasharray: 8px;
          stroke-dashoffset: 0;
          animation: line-move 2s linear infinite;

          @at-root {
            @keyframes line-move {
              0% {
                stroke-dashoffset: 16px;
              }
              100% {
                stroke-dashoffset: 0;
              }
            }
          }
        }
      }
      .diff-mask-full {
        top: 10px;
        height: calc(100% - 10px);
      }
    }
  }
}
.top-icon {
  position: absolute;
  right: 10px;
  bottom: 70px;
  width: 39px;
  height: 39px;
  line-height: 39px;
  text-align: center;
  cursor: pointer;
  background: rgb(0 0 0 / 10%);
  border-radius: 50%;
  .icon-is-zhiding {
    display: inline-block;
    color: rgb(0 0 0 / 10%);
  }
}
</style>

<style>
.canvas-container {
  position: absolute !important;
  top: 0;
  left: 0;
}
.annotationLayer {
  display: none !important;
}
</style>
