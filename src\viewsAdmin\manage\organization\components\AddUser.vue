<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="500px"
    :show-close="true"
    @close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="70px"
      class="demo-ruleForm custom-dialog-form"
      @submit.prevent
    >
      <!-- <el-row :gutter="20">
      </el-row> -->
      <el-col>
        <el-form-item class="is-required" :label="$t('用户账号')" prop="userName">
          <el-input v-model.trim="form.userName" :placeholder="$t('请输入账号')" :maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item class="is-required" :label="$t('用户姓名')" prop="realName">
          <el-input
            v-model.trim="form.realName"
            :placeholder="$t('请输入真实姓名')"
            @blur="getPinying"
            :maxlength="20"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item :label="$t('用户角色')" prop="roleCode">
          <el-select v-model="form.roleCode" multiple class="block" :placeholder="$t('请选择账号角色')">
            <el-option
              v-for="(item, index) in roleList"
              :key="index"
              :label="item.roleName"
              :value="item.roleCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item :label="$t('归属部门')" prop="orgIdList">
          <el-cascader
            ref="cas"
            v-model="form.orgIdList"
            :options="orgList"
            :props="treeProps"
            :placeholder="$t('请选择归属部门')"
            clearable
            style="width: 100%"
            @change="getNode"
          ></el-cascader>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item :label="$t('主部门')" prop="mainDepartment">
          <el-select v-model="form.mainDepartment" style="width: 100%" :placeholder="$t('请选择')">
            <el-option v-for="item in mainOrgList" :key="item.id" :label="item.orgNamePath" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click.stop="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" :loading="loading" @click.stop="submit">{{ $t('确定') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneObject } from '@/utils'
import { addUserRequest, editUserRequest, checkUserName, queryPageRoleList } from '@/services/manage/index'
import { useOrganizationStore } from '@/stores'
import type { FormInstance } from 'element-plus'
import { $t } from '@/utils/i18n'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
// import { UserNode } from '../type'

const organizationStore = useOrganizationStore()

// 方法
const getDefaultForm = () => {
  return {
    realName: '', //
    orgIdList: [], //
    roleCode: [], //
    userName: '', //
    mainDepartment: [], //
  }
}

// 响应式数据
const dialogTitle = ref($t('新建人员'))
const loading = ref(false)
const visible = ref(false)
const customValidDate = ref('')

const treeProps = reactive({
  children: 'children',
  label: 'orgName',
  value: 'id',
  multiple: true,
  checkStrictly: true,
})
const form = reactive({
  realName: '',
  orgIdList: [] as string[],
  roleCode: [] as string[],
  userName: '',
  mainDepartment: '',
  userId: '', // 用于编辑时传递用户ID
})
const mainOrgList = ref<any[]>([]) // 主部门下拉列表
const pickerOptions = reactive({
  disabledDate(time: Date) {
    return time.getTime() < Date.now() - 3600 * 1000 * 24
  },
})
// 模板引用
const ruleForm = ref<FormInstance>()
const cas = ref()
// 计算属性
const orgList = computed(() => organizationStore.orgList)
const roleList = computed(() => organizationStore.roleList)
const roleSelectMap = computed(() => organizationStore.roleSelectMap)
// 表单验证规则
const rules = reactive({
  realName: [
    {
      required: true,
      message: $t('请输入用户姓名'),
      trigger: 'change',
    },
  ],
  orgIdList: [{ required: true, message: $t('请选择部门'), trigger: 'change' }],
  mainDepartment: [{ required: true, message: $t('请选择主部门'), trigger: 'change' }],
  roleCode: [{ required: true, message: $t('请选择角色'), trigger: 'change' }],
  userName: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        let flag = false
        const reg = /^[a-zA-Z0-9_-]{4,20}$/
        if (value) {
          if (!reg.test(value)) {
            callback(new Error($t('4到20位大小写字母数字_-')))
          } else {
            if (value === oldName.value) {
              callback()
              return
            }
            checkUserName({
              userName: value,
            })
              .then((res) => {
                flag = res.data as boolean
                if (!flag) callback(new Error(res.message))
                else callback()
              })
              .catch((err: any) => {
                callback(new Error(err.message))
              })
          }
        } else {
          callback(new Error($t('请输入用户账号')))
        }
      },
      trigger: 'blur',
    },
  ],
})
const operateType = ref(0) // 0: 新增用户, 1: 编辑用户
const oldName = ref('')
// 定义emits
const emit = defineEmits(['succ'])

// 获取主部门下拉选项
const getNode = () => {
  const res = cas.value.getCheckedNodes()
  const result = res.map((item: any) => {
    return item.data
  })
  mainOrgList.value = result
}

const getPinying = () => {
  ruleForm.value?.validateField('realName', async (error: any) => {
    if (!error) {
      const { realName } = form
      // 这里需要实际的API调用
      // const res = await httpApi.getPingyin({ realName })
      // form.userName = res.data || ''
      form.userName = realName // 临时处理
    }
  })
}

const closeDialog = () => {
  visible.value = false
  Object.assign(form, getDefaultForm())
  customValidDate.value = ''
  nextTick(() => {
    ruleForm.value?.resetFields()
    ruleForm.value?.clearValidate()
  })
}

// const searchRoleList = async () => {
//   const params = {
//     data: { roleName: '', startLastUpdateDate: '', endLastUpdateDate: '' },
//     page: 1,
//     pageSize: 1000,
//   }
//   const res = await queryPageRoleList<IQueryPageRoleList>(params)
//   if (res.code == RESPONSE_CODE_SUCCESS) {
//     //  const { result } = res.data || {};
//     console.log(res, 'searchRoleList res')
//     organizationStore.setRoleList(res.data.list || [])
//     //  commit('set_roleSelectList', result || []);
//     //  const roleCodeMap = formatListFunc({
//     //    obj: result,
//     //         key: 'roleCode',
//     //         value: 'roleName',
//     //       });
//     //       commit('set_roleSelectMap', roleCodeMap);
//     //     }
//     //   });
//   }
// }

const openDialog = async (data: any) => {
  //todo: 获取组织list
  // await store.dispatch('org/searchOrgSelect')
  // const res = await searchRoleList()
  operateType.value = data.operateType || 0
  if (operateType.value === 0) {
    // 新增用户
    Object.assign(form, getDefaultForm())
    if (data.selectOrg.length) {
      form.orgIdList = [...data.selectOrg]
      form.mainDepartment = data.selectOrg.slice().pop() || ''
    }
  } else if (operateType.value === 1) {
    // 编辑用户
    oldName.value = data.userName || ''
    const { userId, userName, realName, orgIdList, roleCode, mainDepartment } = data
    form.userId = userId || ''
    form.userName = userName || ''
    form.realName = realName || ''
    form.orgIdList = orgIdList || []
    form.roleCode = roleCode || []
    form.mainDepartment = mainDepartment || ''
    console.log('form', form)
  } else {
    return
  }

  visible.value = true
  nextTick(() => {
    getNode()
    ruleForm.value?.clearValidate()
  })
}

const submit = () => {
  if (loading.value) return false
  loading.value = true
  ruleForm.value?.validate((valid) => {
    if (valid) {
      confirmAdd()
    } else {
      loading.value = false
    }
  })
}

const confirmAdd = async () => {
  const formData = cloneObject(form)

  // const validTime = formData.validTimeStatus
  //   ? ''
  //   : customValidDate.value.indexOf('23:59:59') > -1
  //   ? customValidDate.value
  //   : customValidDate.value + ' 23:59:59'

  console.log('formData', formData)
  // const orgIds: string[] = []
  // formData.orgIdList.map((item: string) => {
  //   return orgIds.push(item)
  // })
  // console.log('orgIds', orgIds)
  if (!formData.orgIdList.includes(formData.mainDepartment)) {
    loading.value = false
    return ElMessage.error($t('主部门和归属部门不匹配，主部门必须是归属部门中的一个。'))
  }

  const params = {
    ...formData,
    // validTime: validTime,
    // orgIdList: orgIds,
  }
  if (operateType.value === 0) {
    const result = await addUserRequest(params).finally(() => {
      loading.value = false
    })
    if (result.code === RESPONSE_CODE_SUCCESS) {
      closeDialog()
      ElMessage.success($t('新建成功'))
      emit('succ')
    }
  } else {
    const result = await editUserRequest(params).finally(() => {
      loading.value = false
    })
    if (result.code === RESPONSE_CODE_SUCCESS) {
      closeDialog()
      ElMessage.success($t('编辑成功'))
      emit('succ')
    }
  }
}

// 暴露方法给父组件使用
defineExpose({
  openDialog,
})
</script>

<style lang="scss" scoped>
.block {
  width: 100%;
}
:deep(.el-input__inner) {
  height: 2rem !important;
}
.data-box {
  display: inline-block;
  width: 200px;
  padding-left: 20px;
}
.custom-dialog-form {
  .el-col {
    min-height: 55px;
  }
  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: normal;
    color: #2a2b2c;
  }
  :deep(.el-radio__label) {
    font-size: 12px;
    font-weight: normal;
  }
  .custom-label-len-5 {
    :deep(.el-form-item__label) {
      width: 77px !important;
    }
    :deep(.el-form-item__error) {
      left: 164px;
    }
  }
  .date-box {
    padding-top: 15px;
  }
}
</style>
