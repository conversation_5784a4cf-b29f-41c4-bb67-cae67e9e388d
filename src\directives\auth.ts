import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores'

// 元素状态存储的键
const ELEMENT_STATE_KEY = '__v_auth_state__'

interface ElementState {
  originalDisplay: string
  originalVisibility: string
  originalDisabled: boolean
  originalReadonly: boolean
}

interface PermissionConfig {
  permissions: string | string[]
  mode?: 'and' | 'or'
  fallback?: 'hide' | 'disable' | 'readonly'
  debug?: boolean
}

/**
 * 权限控制指令
 * 用法：
 * v-auth="'permission'" - 检查单个权限
 * v-auth="['perm1', 'perm2']" - 检查多个权限（OR关系）
 * v-auth:permission - 使用参数形式
 * v-auth:permission.and="['perm1', 'perm2']" - 多个权限（AND关系）
 * v-auth.disable="'permission'" - 无权限时禁用元素而不是隐藏
 * v-auth.readonly="'permission'" - 无权限时设为只读
 * v-auth="{ permissions: ['perm1', 'perm2'], mode: 'and', fallback: 'disable' }" - 配置对象形式
 */
/**
 * 保存元素的原始状态
 */
function saveOriginalState(el: HTMLElement) {
  const state: ElementState = {
    originalDisplay: el.style.display || '',
    originalVisibility: el.style.visibility || '',
    originalDisabled: (el as any).disabled || false,
    originalReadonly: (el as any).readOnly || false,
  }
    ; (el as any)[ELEMENT_STATE_KEY] = state
}

/**
 * 清理元素状态
 */
function cleanupElementState(el: HTMLElement) {
  delete (el as any)[ELEMENT_STATE_KEY]
}

/**
 * 恢复元素的原始状态
 */
function restoreOriginalState(el: HTMLElement) {
  const state: ElementState = (el as any)[ELEMENT_STATE_KEY]
  if (state) {
    el.style.display = state.originalDisplay
    el.style.visibility = state.originalVisibility
      ; (el as any).disabled = state.originalDisabled
      ; (el as any).readOnly = state.originalReadonly
  }
}

const authDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 保存元素的原始状态
    saveOriginalState(el)
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  beforeUnmount(el: HTMLElement) {
    // 清理保存的状态
    cleanupElementState(el)
  },
}

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const userStore = useUserStore()
  const permissionList = userStore.getMenuList()

  let requiredPermissions: string[] = []
  let hasPermission = false
  let config: PermissionConfig | null = null

  // 解析权限配置
  if (binding.arg) {
    // 使用参数形式：v-auth:permission
    requiredPermissions = [binding.arg]
  } else if (binding.value) {
    if (typeof binding.value === 'string') {
      // 单个权限字符串
      requiredPermissions = [binding.value]
    } else if (Array.isArray(binding.value)) {
      // 权限数组
      requiredPermissions = binding.value
    } else if (typeof binding.value === 'object' && binding.value.permissions) {
      // 配置对象形式
      config = binding.value as PermissionConfig
      if (typeof config.permissions === 'string') {
        requiredPermissions = [config.permissions]
      } else {
        requiredPermissions = config.permissions
      }
    }
  }

  if (requiredPermissions.length === 0) {
    console.warn('v-auth directive: No permissions specified')
    return
  }

  // 确定检查模式
  const mode = config?.mode || (binding.modifiers.and ? 'and' : 'or')

  // 检查权限
  if (mode === 'and') {
    // AND关系：所有权限都必须有
    hasPermission = requiredPermissions.every((permission) => permissionList.includes(permission))
  } else {
    // OR关系：有任意一个权限即可（默认）
    hasPermission = requiredPermissions.some((permission) => permissionList.includes(permission))
  }

  // 确定回退行为
  const fallback =
    config?.fallback || (binding.modifiers.disable ? 'disable' : binding.modifiers.readonly ? 'readonly' : 'hide')

  // 添加调试信息 - 总是输出以便排查问题
  console.log('v-auth debug:', {
    element: el.tagName + (el.className ? '.' + el.className : ''),
    requiredPermissions,
    userPermissions: permissionList,
    hasPermission,
    mode,
    fallback,
  })

  // 应用权限控制
  applyPermissionControl(el, hasPermission, fallback)
}

/**
 * 应用权限控制
 */
function applyPermissionControl(el: HTMLElement, hasPermission: boolean, fallback: string) {
  if (hasPermission) {
    // 有权限时恢复原始状态
    restoreOriginalState(el)
  } else {
    // 无权限时根据回退策略处理
    switch (fallback) {
      case 'disable':
        // 禁用元素
        el.style.display = ''
        el.style.visibility = 'visible'
          ; (el as any).disabled = true
        break
      case 'readonly':
        // 设为只读
        el.style.display = ''
        el.style.visibility = 'visible'
          ; (el as any).readOnly = true
          ; (el as any).disabled = false
        break
      case 'hide':
      default:
        // 隐藏元素（默认）
        el.style.display = 'none'
        el.style.visibility = 'hidden'
        break
    }
  }
}

export default authDirective
