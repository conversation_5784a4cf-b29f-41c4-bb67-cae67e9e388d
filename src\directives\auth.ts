import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores'

interface AuthBinding extends DirectiveBinding {
  value?: string | string[]
  arg?: string
}

/**
 * 权限控制指令
 * 用法：
 * v-auth="'permission'" - 检查单个权限
 * v-auth="['perm1', 'perm2']" - 检查多个权限（OR关系）
 * v-auth:permission - 使用参数形式
 * v-auth:permission.and="['perm1', 'perm2']" - 多个权限（AND关系）
 */
const authDirective: Directive = {
  mounted(el: HTMLElement, binding: AuthBinding) {
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: AuthBinding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el: HTMLElement, binding: AuthBinding) {
  const userStore = useUserStore()
  const permissionList = userStore.getMenuList()
  
  let requiredPermissions: string[] = []
  let hasPermission = false
  
  // 获取需要检查的权限
  if (binding.arg) {
    // 使用参数形式：v-auth:permission
    requiredPermissions = [binding.arg]
  } else if (binding.value) {
    // 使用值形式：v-auth="'permission'" 或 v-auth="['perm1', 'perm2']"
    if (typeof binding.value === 'string') {
      requiredPermissions = [binding.value]
    } else if (Array.isArray(binding.value)) {
      requiredPermissions = binding.value
    }
  }
  
  if (requiredPermissions.length === 0) {
    console.warn('v-auth directive: No permissions specified')
    return
  }
  
  // 检查权限
  if (binding.modifiers.and) {
    // AND关系：所有权限都必须有
    hasPermission = requiredPermissions.every(permission => 
      permissionList.includes(permission)
    )
  } else {
    // OR关系：有任意一个权限即可（默认）
    hasPermission = requiredPermissions.some(permission => 
      permissionList.includes(permission)
    )
  }
  
  // 控制元素显示/隐藏
  if (hasPermission) {
    el.style.display = ''
    el.style.visibility = 'visible'
  } else {
    el.style.display = 'none'
    el.style.visibility = 'hidden'
  }
}

export default authDirective
