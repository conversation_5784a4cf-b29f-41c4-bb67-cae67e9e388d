import { createApp } from 'vue'
import 'virtual:svg-icons-register'
import router from './router'
import './styles/index.css'
import './plugins/iconfont/index.scss'
import i18n from './lang/index.ts'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import permission from './permission'
import { setupDirectives } from './directives'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

const app = createApp(App).use(router).use(i18n).use(pinia).use(permission)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册自定义指令
setupDirectives(app)

// 挂载全局方法
app.config.globalProperties.$nodeAuth = () => {
  return true
}

app.config.globalProperties.$nodeOwnAuth = () => {
  return true
}

app.mount('#app')
