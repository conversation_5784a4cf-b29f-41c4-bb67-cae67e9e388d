import { post, get, del } from '@/services'
import type { IResponse } from '@/services'

export interface ICompareRecord {
  page: number
  pageSize: number
  data?: {
    contractName?: string
    progressType?: number
    startTime?: string
    endTime?: string
  }
}

export interface ICompareResponse {
  compareMessage: string
  id: string
  progressNum: number
  updateTime: string
  rightFilename: string
}

export interface ICompareResponseList {
  list: ICompareResponse[]
  total: number
  pageNum: number
  pageSize: number
  pages: number
}

type MyFormFields = 'source' | 'target' | 'ignoreSymbol' | 'ignoreHeadersAndFooters' | 'ignoreWatermark'
interface ICompareFormData extends FormData {
  append(name: MyFormFields, value: string | Blob, fileName?: string): void
}

// 查询最近三条记录
export function queryRecentCompareRecord<T>(): Promise<IResponse<T>> {
  return get<T>(`/compare/recent`)
}

// 分页查询比对列表
export function queryCompareRecord<T>(data: ICompareRecord): Promise<IResponse<T>> {
  return post<T>(`/compare/page`, data)
}

// 删除单条比对记录
export function deleteCompareRecord<T>(id: string): Promise<IResponse<T>> {
  return del<T>(`/compare/remove/${id}`)
}

export function deleteSemanticsRecord<T>(id: string): Promise<IResponse<T>> {
  return del<T>(`/semantic-compare/remove/${id}`)
}

export function querySemanticsCompareRecord<T>(data: ICompareRecord): Promise<IResponse<T>> {
  return post<T>(`/semantic-compare/search`, data)
}

export function compareDiffFile<T>(data: ICompareFormData): Promise<IResponse<T>> {
  return post<T>(`/compare/compare-file-v3`, data)
}

export function getRecentCompareRecordList<T>(): Promise<IResponse<T>> {
  return get<T>(`/compare/recent`)
}

export function getPdfCompare<T>(params: any): Promise<IResponse<T>> {
  return get<T>(`/compare/get-compare-result`, params)
}

export function downloadDiff<T>(params: any): Promise<IResponse<T>> {
  return get<T>(`/compare/diff/export`, params, { responseType: 'blob' })
}

export function querySemantics<T>(data: any): Promise<IResponse<T>> {
  return post<T>(`/semantic-compare/compare`, data)
}

export interface ISemanticsDiff {
  diffContent: string
  index: string
  leftContent: string
  rightContent: string
  semanticSimilarity: string
}
export interface ISemanticsResponse {
  compareResult?: {
    diff?: ISemanticsDiff[]
    leftPdfFilePath: string
    rightPdfFilePath: string
  }
  leftFilename: string
  rightFilename: string
  progressNum: number
}
export function querySemanticResult<T>(params: { id: string }): Promise<IResponse<T>> {
  return get<T>(`/semantic-compare/result`, params)
}
