import type { Directive, DirectiveBinding } from 'vue'

/**
 * 防抖指令
 * 用法：
 * v-debounce="handleClick" - 默认300ms防抖
 * v-debounce:500="handleClick" - 自定义500ms防抖
 * v-debounce.immediate="handleClick" - 立即执行第一次
 * v-debounce="{ handler: handleClick, delay: 1000, immediate: true }" - 配置对象形式
 */
const debounceDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    setupDebounce(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 清除旧的防抖，设置新的
    clearDebounce(el)
    setupDebounce(el, binding)
  },
  beforeUnmount(el: HTMLElement) {
    clearDebounce(el)
  }
}

interface DebounceConfig {
  handler: Function
  delay?: number
  immediate?: boolean
  event?: string
}

function setupDebounce(el: HTMLElement, binding: DirectiveBinding) {
  let handler: Function
  let delay: number
  let immediate: boolean
  let eventType: string

  // 解析配置
  if (typeof binding.value === 'function') {
    handler = binding.value
    delay = binding.arg ? parseInt(binding.arg) : 300
    immediate = binding.modifiers.immediate || false
    eventType = 'click'
  } else if (typeof binding.value === 'object' && binding.value.handler) {
    const config = binding.value as DebounceConfig
    handler = config.handler
    delay = config.delay || (binding.arg ? parseInt(binding.arg) : 300)
    immediate = config.immediate || binding.modifiers.immediate || false
    eventType = config.event || 'click'
  } else {
    console.warn('v-debounce: Invalid handler provided')
    return
  }

  let timeoutId: number | null = null
  let hasExecuted = false

  const debouncedHandler = (...args: any[]) => {
    const callNow = immediate && !hasExecuted

    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = window.setTimeout(() => {
      timeoutId = null
      hasExecuted = true
      if (!immediate) {
        handler.apply(el, args)
      }
    }, delay)

    if (callNow) {
      hasExecuted = true
      handler.apply(el, args)
    }
  }

  // 保存防抖处理函数和定时器ID
  ;(el as any).__debounceHandler = debouncedHandler
  ;(el as any).__debounceTimeoutId = timeoutId

  // 添加事件监听器
  el.addEventListener(eventType, debouncedHandler)
}

function clearDebounce(el: HTMLElement) {
  const handler = (el as any).__debounceHandler
  const timeoutId = (el as any).__debounceTimeoutId

  if (handler) {
    el.removeEventListener('click', handler)
    delete (el as any).__debounceHandler
  }

  if (timeoutId) {
    clearTimeout(timeoutId)
    delete (el as any).__debounceTimeoutId
  }
}

export default debounceDirective
