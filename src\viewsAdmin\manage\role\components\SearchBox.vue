<script setup lang="ts">
import type { IRoleQueryData } from '@/services/manage/index'
import { formatTime } from '@/utils/index'

const emits = defineEmits(['search'])
const initData = {
  roleName: '',
  updateDateRange: [],
}

const formInline = ref({ ...initData })

const handleReset = () => {
  formInline.value = { ...initData }
  handleSearch()
}

const handleSearch = () => {
  const params: IRoleQueryData = {
    roleName: formInline.value.roleName,
    startLastUpdateDate: formInline.value.updateDateRange.length ? formatTime(formInline.value.updateDateRange[0]) : '',
    endLastUpdateDate: formInline.value.updateDateRange.length ? formatTime(formInline.value.updateDateRange[1]) : '',
  }
  emits('search', params)
}
</script>

<template>
  <div class="condition">
    <span class="condition-label">{{ $t('角色名称') }}</span>
    <el-input
      v-model="formInline.roleName"
      clearable
      :placeholder="$t('请输入角色名称')"
      show-word-limit
      maxlength="20"
    />
  </div>
  <div class="condition">
    <span class="condition-label">{{ $t('更新时间') }}</span>
    <!--  :shortcuts="shortcuts" -->
    <el-date-picker
      v-model="formInline.updateDateRange"
      type="daterange"
      range-:separator="$t('至')"
      start-:placeholder="$t('开始时间')"
      end-:placeholder="$t('结束时间')"
    />
  </div>
  <div class="btn-wrap">
    <el-button class="btn" type="primary" @click="handleSearch">{{ $t('查 询') }}</el-button>
    <el-button class="secondary" @click="handleReset">{{ $t('重 置') }}</el-button>
  </div>
</template>
