import type { Directive, DirectiveBinding } from 'vue'

/**
 * 点击外部区域指令
 * 用法：
 * v-click-outside="handleClickOutside" - 点击元素外部时触发回调
 * v-click-outside.stop="handleClickOutside" - 阻止事件冒泡
 * v-click-outside.prevent="handleClickOutside" - 阻止默认行为
 */
const clickOutsideDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const handler = (event: Event) => {
      // 检查点击是否在元素内部
      if (el.contains(event.target as Node)) {
        return
      }

      // 检查修饰符
      if (binding.modifiers.stop) {
        event.stopPropagation()
      }
      if (binding.modifiers.prevent) {
        event.preventDefault()
      }

      // 调用回调函数
      if (typeof binding.value === 'function') {
        binding.value(event)
      }
    }

    // 保存处理函数引用以便后续移除
    ;(el as any).__clickOutsideHandler = handler
    
    // 延迟添加事件监听器，避免立即触发
    setTimeout(() => {
      document.addEventListener('click', handler, true)
    }, 0)
  },

  beforeUnmount(el: HTMLElement) {
    const handler = (el as any).__clickOutsideHandler
    if (handler) {
      document.removeEventListener('click', handler, true)
      delete (el as any).__clickOutsideHandler
    }
  }
}

export default clickOutsideDirective
