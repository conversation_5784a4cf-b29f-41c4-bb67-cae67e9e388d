import { downloadStream } from '@/services/common.ts'
import { exportFile } from '@/utils'

interface DownloadOptions {
  fileCode?: string
  fileName?: string
  fileApi?: Array<any>
}

export function useDownloadFile() {
  const downloadFile = async (obj: DownloadOptions): Promise<void> => {
    let { fileName } = obj
    const { fileCode } = obj
    const { fileApi } = obj
    let resData

    if (!fileApi) {
      resData = await downloadStream({
        fileCode: fileCode!,
        fileName: fileName || '',
      })
    } else {
      const [api, params] = fileApi
      resData = await api(params)
    }

    // 处理文件名
    if (!fileName) {
      fileName = resData.headers['content-disposition']?.split(';')[1]?.split('=')[1] || ''
      fileName = decodeURI(fileName as string)
    } else if (fileCode) {
      let suffix = fileCode.substring(fileCode.lastIndexOf('.'))
      if (fileName.lastIndexOf(suffix) !== -1) {
        fileName = fileName.substring(0, fileName.lastIndexOf(suffix))
      }

      if (suffix === fileCode) {
        // fileCode 被加密，没有后缀
        let _fileName = resData.headers['content-disposition']?.split(';')[1]?.split('=')[1] || ''
        _fileName = decodeURI(_fileName)
        suffix = _fileName.substring(_fileName.lastIndexOf('.'))
        if (fileName.lastIndexOf(suffix) !== -1) {
          fileName = fileName.substring(0, fileName.lastIndexOf(suffix))
        }
      }
      fileName = fileName + suffix
    }

    exportFile(resData.data, fileName)
  }

  return {
    downloadFile,
  }
}
